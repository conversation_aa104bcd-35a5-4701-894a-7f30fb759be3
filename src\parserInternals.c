/*
 * parserInternals.c : XML 和 HTML 解析器所需的内部例程（以及过时的例程）。
 *
 *
 *
 *
 */

#define IN_LIBXML
#include "libxml.h"

#if defined(_WIN32)
#define XML_DIR_SEP '\\'
#else
#define XML_DIR_SEP '/'
#endif

#include <string.h>
#include <ctype.h>
#include <stdlib.h>

#include <libxml/xmlmemory.h>
#include <libxml/tree.h>
#include <libxml/parser.h>
#include <libxml/parserInternals.h>
#include <libxml/entities.h>
#include <libxml/xmlerror.h>
#include <libxml/encoding.h>
#include <libxml/xmlIO.h>
#include <libxml/uri.h>
#include <libxml/dict.h>
#include <libxml/xmlsave.h>
#ifdef LIBXML_CATALOG_ENABLED
#include <libxml/catalog.h>
#endif
#include <libxml/chvalid.h>

#define CUR(ctxt) ctxt->input->cur
#define END(ctxt) ctxt->input->end

#include "private/buf.h"
#include "private/enc.h"
#include "private/error.h"
#include "private/io.h"
#include "private/memory.h"
#include "private/parser.h"

#ifndef SIZE_MAX
  #define SIZE_MAX ((size_t) -1)
#endif

#define XML_MAX_ERRORS 100

/*
 * XML_MAX_AMPLIFICATION_DEFAULT 是实体扩展后序列化输出的默认最大允许放大因子。
 */
#define XML_MAX_AMPLIFICATION_DEFAULT 5

/*
 * 解析的各种全局默认值
 */

/**
 * 检查编译的库版本与包含的版本是否一致。
 *
 * @param version  包含的版本号
 */
void
xmlCheckVersion(int version) {
    int myversion = LIBXML_VERSION;

    xmlInitParser();

    if ((myversion / 10000) != (version / 10000)) {
	xmlPrintErrorMessage(
		"Fatal: program compiled against libxml %d using libxml %d\n",
		(version / 10000), (myversion / 10000));
    } else if ((myversion / 100) < (version / 100)) {
	xmlPrintErrorMessage(
		"Warning: program compiled against libxml %d using older %d\n",
		(version / 100), (myversion / 100));
    }
}


/************************************************************************
 *									*
 *		一些因式分解的错误例程					*
 *									*
 ************************************************************************/


/**
 * 注册一个回调函数，该函数将在错误和警告时被调用。如果 handler 为 NULL，
 * 错误处理程序将被停用。
 *
 * 这是从解析器收集错误的推荐方式，优先于所有其他错误报告机制。
 * 这些机制按优先级顺序为：
 *
 * - 每个上下文的结构化处理程序 (xmlCtxtSetErrorHandler())
 * - 每个上下文的结构化 "serror" SAX 处理程序
 * - 全局结构化处理程序 (xmlSetStructuredErrorFunc())
 * - 每个上下文的通用 "error" 和 "warning" SAX 处理程序
 * - 全局通用处理程序 (xmlSetGenericErrorFunc())
 * - 打印到 stderr
 *
 * @since 2.13.0
 * @param ctxt  XML 解析器上下文
 * @param handler  错误处理程序
 * @param data  错误处理程序的数据
 */
void
xmlCtxtSetErrorHandler(xmlParserCtxtPtr ctxt, xmlStructuredErrorFunc handler,
                       void *data)
{
    if (ctxt == NULL)
        return;
    ctxt->errorHandler = handler;
    ctxt->errorCtxt = data;
}

/**
 * 获取最后一个引发的错误。
 *
 * 请注意，XML 解析器通常不会在遇到错误后停止，
 * 并且经常会报告多个错误。大多数时候，最后一个错误并不有用。
 * 未来版本可能会返回第一个解析器错误。
 *
 * @param ctx  XML 解析器上下文
 * @returns 如果没有发生错误则返回 NULL，否则返回指向错误的指针
 */
const xmlError *
xmlCtxtGetLastError(void *ctx)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;

    if (ctxt == NULL)
        return (NULL);
    if (ctxt->lastError.code == XML_ERR_OK)
        return (NULL);
    return (&ctxt->lastError);
}

/**
 * 将最后一个解析器错误重置为成功。这不会改变格式良好状态。
 *
 * @param ctx  XML 解析器上下文
 */
void
xmlCtxtResetLastError(void *ctx)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;

    if (ctxt == NULL)
        return;
    ctxt->errNo = XML_ERR_OK;
    if (ctxt->lastError.code == XML_ERR_OK)
        return;
    xmlResetError(&ctxt->lastError);
}

/**
 * 处理内存不足错误。
 *
 * @since 2.13.0
 * @param ctxt  XML 解析器上下文
 */
void
xmlCtxtErrMemory(xmlParserCtxtPtr ctxt)
{
    xmlStructuredErrorFunc schannel = NULL;
    xmlGenericErrorFunc channel = NULL;
    void *data;

    if (ctxt == NULL) {
        xmlRaiseMemoryError(NULL, NULL, NULL, XML_FROM_PARSER, NULL);
        return;
    }

    ctxt->errNo = XML_ERR_NO_MEMORY;
    ctxt->instate = XML_PARSER_EOF; /* TODO: Remove after refactoring */
    ctxt->wellFormed = 0;
    ctxt->disableSAX = 2;

    if (ctxt->errorHandler) {
        schannel = ctxt->errorHandler;
        data = ctxt->errorCtxt;
    } else if ((ctxt->sax->initialized == XML_SAX2_MAGIC) &&
        (ctxt->sax->serror != NULL)) {
        schannel = ctxt->sax->serror;
        data = ctxt->userData;
    } else {
        channel = ctxt->sax->error;
        data = ctxt->userData;
    }

    xmlRaiseMemoryError(schannel, channel, data, XML_FROM_PARSER,
                        &ctxt->lastError);
}

/**
 * 如果文件名为空，则使用上下文输入中的文件名（如果可用）。
 *
 * 向解析器上下文报告 IO 错误。
 * @param ctxt  解析器上下文
 * @param code  xmlParserErrors 代码
 * @param uri  文件名或 URI（可选）
 */
void
xmlCtxtErrIO(xmlParserCtxtPtr ctxt, int code, const char *uri)
{
    const char *errstr, *msg, *str1, *str2;
    xmlErrorLevel level;

    if (ctxt == NULL)
        return;

    if (((code == XML_IO_ENOENT) ||
         (code == XML_IO_UNKNOWN))) {
        /*
         * Only report a warning if a file could not be found. This should
         * only be done for external entities, but the external entity loader
         * of xsltproc can try multiple paths and assumes that ENOENT doesn't
         * raise an error and aborts parsing.
         */
        if (ctxt->validate == 0)
            level = XML_ERR_WARNING;
        else
            level = XML_ERR_ERROR;
    } else if (code == XML_IO_NETWORK_ATTEMPT) {
        level = XML_ERR_ERROR;
    } else {
        level = XML_ERR_FATAL;
    }

    errstr = xmlErrString(code);

    if (uri == NULL) {
        msg = "%s\n";
        str1 = errstr;
        str2 = NULL;
    } else {
        msg = "failed to load \"%s\": %s\n";
        str1 = uri;
        str2 = errstr;
    }

    xmlCtxtErr(ctxt, NULL, XML_FROM_IO, code, level,
               (const xmlChar *) uri, NULL, NULL, 0,
               msg, str1, str2);
}

/**
 * @param ctxt  解析器上下文
 * @returns 如果最后一个错误是灾难性的则返回 true。
 */
int
xmlCtxtIsCatastrophicError(xmlParserCtxtPtr ctxt) {
    if (ctxt == NULL)
        return(1);

    return(xmlIsCatastrophicError(ctxt->lastError.level,
                                  ctxt->lastError.code));
}

/**
 * 引发解析器错误。
 *
 * @param ctxt  解析器上下文
 * @param node  当前节点或 NULL
 * @param domain  错误的域
 * @param code  错误代码
 * @param level  错误的 xmlErrorLevel
 * @param str1  额外的字符串信息
 * @param str2  额外的字符串信息
 * @param str3  额外的字符串信息
 * @param int1  额外的整数信息
 * @param msg  要显示/传输的消息
 * @param ap  消息显示的额外参数
 */
void
xmlCtxtVErr(xmlParserCtxtPtr ctxt, xmlNodePtr node, xmlErrorDomain domain,
            xmlParserErrors code, xmlErrorLevel level,
            const xmlChar *str1, const xmlChar *str2, const xmlChar *str3,
            int int1, const char *msg, va_list ap)
{
    xmlStructuredErrorFunc schannel = NULL;
    xmlGenericErrorFunc channel = NULL;
    void *data = NULL;
    const char *file = NULL;
    int line = 0;
    int col = 0;
    int res;

    if (code == XML_ERR_NO_MEMORY) {
        xmlCtxtErrMemory(ctxt);
        return;
    }

    if (ctxt == NULL) {
        res = xmlVRaiseError(NULL, NULL, NULL, NULL, node, domain, code,
                             level, NULL, 0, (const char *) str1,
                             (const char *) str2, (const char *) str3,
                             int1, 0, msg, ap);
        if (res < 0)
            xmlRaiseMemoryError(NULL, NULL, NULL, XML_FROM_PARSER, NULL);

        return;
    }

    if (PARSER_STOPPED(ctxt))
	return;

    /* 不要覆盖灾难性错误 */
    if (xmlCtxtIsCatastrophicError(ctxt))
        return;

    if (level == XML_ERR_WARNING) {
        if (ctxt->nbWarnings >= XML_MAX_ERRORS)
            return;
        ctxt->nbWarnings += 1;
    } else {
        /* 至少报告一个致命错误。 */
        if ((ctxt->nbErrors >= XML_MAX_ERRORS) &&
            ((level < XML_ERR_FATAL) || (ctxt->wellFormed == 0)) &&
            (!xmlIsCatastrophicError(level, code)))
            return;
        ctxt->nbErrors += 1;
    }

    if (((ctxt->options & XML_PARSE_NOERROR) == 0) &&
        ((level != XML_ERR_WARNING) ||
         ((ctxt->options & XML_PARSE_NOWARNING) == 0))) {
        if (ctxt->errorHandler) {
            schannel = ctxt->errorHandler;
            data = ctxt->errorCtxt;
        } else if ((ctxt->sax->initialized == XML_SAX2_MAGIC) &&
            (ctxt->sax->serror != NULL)) {
            schannel = ctxt->sax->serror;
            data = ctxt->userData;
        } else if ((domain == XML_FROM_VALID) || (domain == XML_FROM_DTD)) {
            if (level == XML_ERR_WARNING)
                channel = ctxt->vctxt.warning;
            else
                channel = ctxt->vctxt.error;
            data = ctxt->vctxt.userData;
        } else {
            if (level == XML_ERR_WARNING)
                channel = ctxt->sax->warning;
            else
                channel = ctxt->sax->error;
            data = ctxt->userData;
        }
    }

    if (ctxt->input != NULL) {
        xmlParserInputPtr input = ctxt->input;

        if ((input->filename == NULL) &&
            (ctxt->inputNr > 1)) {
            input = ctxt->inputTab[ctxt->inputNr - 2];
        }
        file = input->filename;
        line = input->line;
        col = input->col;
    }

    res = xmlVRaiseError(schannel, channel, data, ctxt, node, domain, code,
                         level, file, line, (const char *) str1,
                         (const char *) str2, (const char *) str3, int1, col,
                         msg, ap);

    if (res < 0) {
        xmlCtxtErrMemory(ctxt);
        return;
    }

    if (level >= XML_ERR_ERROR)
        ctxt->errNo = code;
    if (level == XML_ERR_FATAL) {
        ctxt->wellFormed = 0;

        if (xmlCtxtIsCatastrophicError(ctxt))
            ctxt->disableSAX = 2; /* stop parser */
        else if (ctxt->recovery == 0)
            ctxt->disableSAX = 1;
    }
}

/**
 * 引发解析器错误。
 *
 * @param ctxt  解析器上下文
 * @param node  当前节点或 NULL
 * @param domain  错误的域
 * @param code  错误代码
 * @param level  错误的 xmlErrorLevel
 * @param str1  额外的字符串信息
 * @param str2  额外的字符串信息
 * @param str3  额外的字符串信息
 * @param int1  额外的整数信息
 * @param msg  要显示/传输的消息
 * @param ...  消息显示的额外参数
 */
void
xmlCtxtErr(xmlParserCtxtPtr ctxt, xmlNodePtr node, xmlErrorDomain domain,
           xmlParserErrors code, xmlErrorLevel level,
           const xmlChar *str1, const xmlChar *str2, const xmlChar *str3,
           int int1, const char *msg, ...)
{
    va_list ap;

    va_start(ap, msg);
    xmlCtxtVErr(ctxt, node, domain, code, level,
                str1, str2, str3, int1, msg, ap);
    va_end(ap);
}

/**
 * 解析后获取格式良好性和验证状态。还报告与解析无关的灾难性错误，
 * 如内存不足、I/O 或其他错误。
 *
 * @since 2.14.0
 *
 * @param ctxt  XML 解析器上下文
 * @returns XML_STATUS_* 标志的位掩码进行 OR 运算。
 */
xmlParserStatus
xmlCtxtGetStatus(xmlParserCtxt *ctxt) {
    xmlParserStatus bits = 0;

    if (xmlCtxtIsCatastrophicError(ctxt)) {
        bits |= XML_STATUS_CATASTROPHIC_ERROR |
                XML_STATUS_NOT_WELL_FORMED |
                XML_STATUS_NOT_NS_WELL_FORMED;
        if ((ctxt != NULL) && (ctxt->validate))
            bits |= XML_STATUS_DTD_VALIDATION_FAILED;

        return(bits);
    }

    if (!ctxt->wellFormed)
        bits |= XML_STATUS_NOT_WELL_FORMED;
    if (!ctxt->nsWellFormed)
        bits |= XML_STATUS_NOT_NS_WELL_FORMED;
    if ((ctxt->validate) && (!ctxt->valid))
        bits |= XML_STATUS_DTD_VALIDATION_FAILED;

    return(bits);
}

/**
 * 处理致命的解析器错误，即违反格式良好性约束
 *
 * @param ctxt  XML 解析器上下文
 * @param code  错误号
 * @param info  额外信息字符串
 */
void
xmlFatalErr(xmlParserCtxtPtr ctxt, xmlParserErrors code, const char *info)
{
    const char *errmsg;

    errmsg = xmlErrString(code);

    if (info == NULL) {
        xmlCtxtErr(ctxt, NULL, XML_FROM_PARSER, code, XML_ERR_FATAL,
                   NULL, NULL, NULL, 0, "%s\n", errmsg);
    } else {
        xmlCtxtErr(ctxt, NULL, XML_FROM_PARSER, code, XML_ERR_FATAL,
                   (const xmlChar *) info, NULL, NULL, 0,
                   "%s: %s\n", errmsg, info);
    }
}

/**
 * 检查字符是否被产生式允许
 *
 * @deprecated 内部函数，请勿使用。
 *
 * ```
 * [84] Letter ::= BaseChar | Ideographic
 * ```
 *
 * @param c  一个 unicode 字符 (int)
 * @returns 如果不允许则返回 0，否则返回非零值
 */
int
xmlIsLetter(int c) {
    return(IS_BASECHAR(c) || IS_IDEOGRAPHIC(c));
}

/************************************************************************
 *									*
 *		渐进式解析的输入处理函数				*
 *									*
 ************************************************************************/

/* 我们需要保留足够的输入以在上下文中显示错误 */
#define LINE_LEN        80

/**
 * 阻止进一步的解析器处理，不要覆盖错误
 * 供内部使用
 *
 * @param ctxt  XML 解析器上下文
 */
void
xmlHaltParser(xmlParserCtxtPtr ctxt) {
    if (ctxt == NULL)
        return;
    ctxt->instate = XML_PARSER_EOF; /* TODO: Remove after refactoring */
    ctxt->disableSAX = 2;
}

/**
 * @deprecated 此函数是内部函数且已弃用。
 *
 * @param in  XML 解析器输入
 * @param len  前瞻的指示性大小
 * @returns -1，因为使用它是错误的。
 */
int
xmlParserInputRead(xmlParserInputPtr in ATTRIBUTE_UNUSED, int len ATTRIBUTE_UNUSED) {
    return(-1);
}

/**
 * 增长输入缓冲区。
 *
 * @param ctxt  XML 解析器上下文
 * @returns 读取的字节数，或在出错时返回 -1。
 */
int
xmlParserGrow(xmlParserCtxtPtr ctxt) {
    xmlParserInputPtr in = ctxt->input;
    xmlParserInputBufferPtr buf = in->buf;
    size_t curEnd = in->end - in->cur;
    size_t curBase = in->cur - in->base;
    size_t maxLength = (ctxt->options & XML_PARSE_HUGE) ?
                       XML_MAX_HUGE_LENGTH :
                       XML_MAX_LOOKUP_LIMIT;
    int ret;

    if (buf == NULL)
        return(0);
    /* 不要增长推送解析器缓冲区。 */
    if (PARSER_PROGRESSIVE(ctxt))
        return(0);
    /* 不要增长内存缓冲区。 */
    if ((buf->encoder == NULL) && (buf->readcallback == NULL))
        return(0);
    if (buf->error != 0)
        return(-1);

    if (curBase > maxLength) {
        xmlFatalErr(ctxt, XML_ERR_RESOURCE_LIMIT,
                    "Buffer size limit exceeded, try XML_PARSE_HUGE\n");
        xmlHaltParser(ctxt);
	return(-1);
    }

    if (curEnd >= INPUT_CHUNK)
        return(0);

    ret = xmlParserInputBufferGrow(buf, INPUT_CHUNK);
    xmlBufUpdateInput(buf->buffer, in, curBase);

    if (ret < 0) {
        xmlCtxtErrIO(ctxt, buf->error, NULL);
    }

    return(ret);
}

/**
 * 如果输入没有完全消耗，则引发带有 `code` 的错误。
 *
 * @param ctxt  解析器上下文
 * @param code  错误代码
 */
void
xmlParserCheckEOF(xmlParserCtxtPtr ctxt, xmlParserErrors code) {
    xmlParserInputPtr in = ctxt->input;
    xmlParserInputBufferPtr buf;

    if (ctxt->errNo != XML_ERR_OK)
        return;

    if (in->cur < in->end) {
        xmlFatalErr(ctxt, code, NULL);
        return;
    }

    buf = in->buf;
    if ((buf != NULL) && (buf->encoder != NULL)) {
        size_t curBase = in->cur - in->base;
        size_t sizeOut = 64;
        xmlCharEncError ret;

        /*
         * 检查截断的多字节序列
         */
        ret = xmlCharEncInput(buf, &sizeOut, /* flush */ 1);
        xmlBufUpdateInput(buf->buffer, in, curBase);
        if (ret != XML_ENC_ERR_SUCCESS) {
            xmlCtxtErrIO(ctxt, buf->error, NULL);
            return;
        }

        /* 不应该发生 */
        if (in->cur < in->end)
            xmlFatalErr(ctxt, XML_ERR_INTERNAL_ERROR, "expected EOF");
    }
}

/**
 * 此函数增加解析器的输入。它尝试保留指向输入缓冲区的指针，
 * 并保持已读取的数据
 *
 * @deprecated 请勿使用。
 *
 * @param in  XML 解析器输入
 * @param len  前瞻的指示性大小
 * @returns 读取的字符数，或在出错时返回 -1，0 表示此实体的结束
 */
int
xmlParserInputGrow(xmlParserInputPtr in, int len) {
    int ret;
    size_t indx;

    if ((in == NULL) || (len < 0)) return(-1);
    if (in->buf == NULL) return(-1);
    if (in->base == NULL) return(-1);
    if (in->cur == NULL) return(-1);
    if (in->buf->buffer == NULL) return(-1);

    /* 不要增长内存缓冲区。 */
    if ((in->buf->encoder == NULL) && (in->buf->readcallback == NULL))
        return(0);

    indx = in->cur - in->base;
    if (xmlBufUse(in->buf->buffer) > (unsigned int) indx + INPUT_CHUNK) {
        return(0);
    }
    ret = xmlParserInputBufferGrow(in->buf, len);

    in->base = xmlBufContent(in->buf->buffer);
    if (in->base == NULL) {
        in->base = BAD_CAST "";
        in->cur = in->base;
        in->end = in->base;
        return(-1);
    }
    in->cur = in->base + indx;
    in->end = xmlBufEnd(in->buf->buffer);

    return(ret);
}

/**
 * 收缩输入缓冲区。
 *
 * @param ctxt  XML 解析器上下文
 */
void
xmlParserShrink(xmlParserCtxtPtr ctxt) {
    xmlParserInputPtr in = ctxt->input;
    xmlParserInputBufferPtr buf = in->buf;
    size_t used, res;

    if (buf == NULL)
        return;

    used = in->cur - in->base;

    if (used > LINE_LEN) {
        res = xmlBufShrink(buf->buffer, used - LINE_LEN);

        if (res > 0) {
            used -= res;
            if ((res > ULONG_MAX) ||
                (in->consumed > ULONG_MAX - (unsigned long)res))
                in->consumed = ULONG_MAX;
            else
                in->consumed += res;
        }

        xmlBufUpdateInput(buf->buffer, in, used);
    }
}

/**
 * 此函数移除解析器使用的输入。
 *
 * @deprecated 请勿使用。
 *
 * @param in  XML 解析器输入
 */
void
xmlParserInputShrink(xmlParserInputPtr in) {
    size_t used;
    size_t ret;

    if (in == NULL) return;
    if (in->buf == NULL) return;
    if (in->base == NULL) return;
    if (in->cur == NULL) return;
    if (in->buf->buffer == NULL) return;

    used = in->cur - in->base;

    if (used > LINE_LEN) {
	ret = xmlBufShrink(in->buf->buffer, used - LINE_LEN);
	if (ret > 0) {
            used -= ret;
            if ((ret > ULONG_MAX) ||
                (in->consumed > ULONG_MAX - (unsigned long)ret))
                in->consumed = ULONG_MAX;
            else
                in->consumed += ret;
	}

        xmlBufUpdateInput(in->buf->buffer, in, used);
    }
}

/************************************************************************
 *									*
 *		UTF8 字符输入和相关函数					*
 *									*
 ************************************************************************/

/**
 * 跳到下一个输入字符。
 *
 * @deprecated 内部函数，请勿使用。
 *
 * @param ctxt  XML 解析器上下文
 */

void
xmlNextChar(xmlParserCtxtPtr ctxt)
{
    const unsigned char *cur;
    size_t avail;
    int c;

    if ((ctxt == NULL) || (ctxt->input == NULL))
        return;

    avail = ctxt->input->end - ctxt->input->cur;

    if (avail < INPUT_CHUNK) {
        xmlParserGrow(ctxt);
        if (ctxt->input->cur >= ctxt->input->end)
            return;
        avail = ctxt->input->end - ctxt->input->cur;
    }

    cur = ctxt->input->cur;
    c = *cur;

    if (c < 0x80) {
        if (c == '\n') {
            ctxt->input->cur++;
            ctxt->input->line++;
            ctxt->input->col = 1;
        } else if (c == '\r') {
            /*
             *   2.11 行尾处理
             *   字面两字符序列 "#xD#xA" 或独立的字面 #xD，
             *   XML 处理器必须向应用程序传递单个字符 #xA。
             */
            ctxt->input->cur += ((cur[1] == '\n') ? 2 : 1);
            ctxt->input->line++;
            ctxt->input->col = 1;
            return;
        } else {
            ctxt->input->cur++;
            ctxt->input->col++;
        }
    } else {
        ctxt->input->col++;

        if ((avail < 2) || (cur[1] & 0xc0) != 0x80)
            goto encoding_error;

        if (c < 0xe0) {
            /* 2 字节代码 */
            if (c < 0xc2)
                goto encoding_error;
            ctxt->input->cur += 2;
        } else {
            unsigned int val = (c << 8) | cur[1];

            if ((avail < 3) || (cur[2] & 0xc0) != 0x80)
                goto encoding_error;

            if (c < 0xf0) {
                /* 3 字节代码 */
                if ((val < 0xe0a0) || ((val >= 0xeda0) && (val < 0xee00)))
                    goto encoding_error;
                ctxt->input->cur += 3;
            } else {
                if ((avail < 4) || ((cur[3] & 0xc0) != 0x80))
                    goto encoding_error;

                /* 4 字节代码 */
                if ((val < 0xf090) || (val >= 0xf490))
                    goto encoding_error;
                ctxt->input->cur += 4;
            }
        }
    }

    return;

encoding_error:
    /* 只报告第一个错误 */
    if ((ctxt->input->flags & XML_INPUT_ENCODING_ERROR) == 0) {
        xmlCtxtErrIO(ctxt, XML_ERR_INVALID_ENCODING, NULL);
        ctxt->input->flags |= XML_INPUT_ENCODING_ERROR;
    }
    ctxt->input->cur++;
}

/**
 * 当前字符值，如果使用 UTF-8，这可能实际上跨越输入缓冲区中的多个字节。
 * 实现行尾规范化：
 *
 * @deprecated 内部函数，请勿使用。
 *
 * 2.11 行尾处理
 *
 * 无论外部解析实体或内部解析实体的字面实体值包含字面两字符序列 "#xD#xA"
 * 或独立的字面 \#xD，XML 处理器都必须向应用程序传递单个字符 \#xA。
 * 这种行为可以通过在解析之前将输入中的所有换行符规范化为 \#xA 来方便地产生。
 *
 * @param ctxt  XML 解析器上下文
 * @param len  指向读取字符长度的指针
 * @returns 当前字符值及其长度
 */

int
xmlCurrentChar(xmlParserCtxtPtr ctxt, int *len) {
    const unsigned char *cur;
    size_t avail;
    int c;

    if ((ctxt == NULL) || (len == NULL) || (ctxt->input == NULL)) return(0);

    avail = ctxt->input->end - ctxt->input->cur;

    if (avail < INPUT_CHUNK) {
        xmlParserGrow(ctxt);
        avail = ctxt->input->end - ctxt->input->cur;
    }

    cur = ctxt->input->cur;
    c = *cur;

    if (c < 0x80) {
	/* 1 字节代码 */
        if (c < 0x20) {
            /*
             *   2.11 行尾处理
             *   字面两字符序列 "#xD#xA" 或独立的字面 #xD，
             *   XML 处理器必须向应用程序传递单个字符 #xA。
             */
            if (c == '\r') {
                /*
                 * TODO: 此函数不应该作为副作用改变 'cur' 指针，
                 * 但 parser.c 中的 NEXTL 宏在递增行号时依赖于此行为。
                 */
                if (cur[1] == '\n')
                    ctxt->input->cur++;
                *len = 1;
                c = '\n';
            } else if (c == 0) {
                if (ctxt->input->cur >= ctxt->input->end) {
                    *len = 0;
                } else {
                    *len = 1;
                    /*
                     * TODO: 空字节应该由调用者处理，但这可能很棘手。
                     */
                    xmlFatalErr(ctxt, XML_ERR_INVALID_CHAR,
                            "Char 0x0 out of allowed range\n");
                }
            } else {
                *len = 1;
            }
        } else {
            *len = 1;
        }

        return(c);
    } else {
        int val;

        if (avail < 2)
            goto incomplete_sequence;
        if ((cur[1] & 0xc0) != 0x80)
            goto encoding_error;

        if (c < 0xe0) {
            /* 2 字节代码 */
            if (c < 0xc2)
                goto encoding_error;
            val = (c & 0x1f) << 6;
            val |= cur[1] & 0x3f;
            *len = 2;
        } else {
            if (avail < 3)
                goto incomplete_sequence;
            if ((cur[2] & 0xc0) != 0x80)
                goto encoding_error;

            if (c < 0xf0) {
                /* 3 字节代码 */
                val = (c & 0xf) << 12;
                val |= (cur[1] & 0x3f) << 6;
                val |= cur[2] & 0x3f;
                if ((val < 0x800) || ((val >= 0xd800) && (val < 0xe000)))
                    goto encoding_error;
                *len = 3;
            } else {
                if (avail < 4)
                    goto incomplete_sequence;
                if ((cur[3] & 0xc0) != 0x80)
                    goto encoding_error;

                /* 4 字节代码 */
                val = (c & 0x0f) << 18;
                val |= (cur[1] & 0x3f) << 12;
                val |= (cur[2] & 0x3f) << 6;
                val |= cur[3] & 0x3f;
                if ((val < 0x10000) || (val >= 0x110000))
                    goto encoding_error;
                *len = 4;
            }
        }

        return(val);
    }

encoding_error:
    /* 只报告第一个错误 */
    if ((ctxt->input->flags & XML_INPUT_ENCODING_ERROR) == 0) {
        xmlCtxtErrIO(ctxt, XML_ERR_INVALID_ENCODING, NULL);
        ctxt->input->flags |= XML_INPUT_ENCODING_ERROR;
    }
    *len = 1;
    return(XML_INVALID_CHAR);

incomplete_sequence:
    /*
     * 编码问题可能由截断的输入缓冲区在中间分割字符而产生。
     * 在这种情况下不要引发错误，而是返回 0。这应该只在推送解析字符数据时发生。
     */
    *len = 0;
    return(0);
}

/**
 * 当前字符值，如果使用 UTF-8，这可能实际上跨越输入缓冲区中的多个字节。
 *
 * @deprecated 内部函数，请勿使用。
 *
 * @param ctxt  XML 解析器上下文
 * @param cur  指向字符开始的指针
 * @param len  指向读取字符长度的指针
 * @returns 当前字符值及其长度
 */

int
xmlStringCurrentChar(xmlParserCtxtPtr ctxt ATTRIBUTE_UNUSED,
                     const xmlChar *cur, int *len) {
    int c;

    if ((cur == NULL) || (len == NULL))
        return(0);

    /* cur 是以零结尾的，所以我们可以谎报其长度。 */
    *len = 4;
    c = xmlGetUTF8Char(cur, len);

    return((c < 0) ? 0 : c);
}

/**
 * 在数组中追加字符值
 *
 * @deprecated 内部函数，请勿使用。
 *
 * @param out  指向 xmlChar 数组的指针
 * @param val  字符值
 * @returns 写入的 xmlChar 数量
 */
int
xmlCopyCharMultiByte(xmlChar *out, int val) {
    if ((out == NULL) || (val < 0)) return(0);
    /*
     * 我们应该处理 UTF8，检查它是否有效
     * 来自 rfc2044：Unicode 值在 UTF-8 上的编码：
     *
     * UCS-4 范围 (十六进制)        UTF-8 八位字节序列 (二进制)
     * 0000 0000-0000 007F   0xxxxxxx
     * 0000 0080-0000 07FF   110xxxxx 10xxxxxx
     * 0000 0800-0000 FFFF   1110xxxx 10xxxxxx 10xxxxxx
     */
    if  (val >= 0x80) {
	xmlChar *savedout = out;
	int bits;
	if (val <   0x800) { *out++= (val >>  6) | 0xC0;  bits=  0; }
	else if (val < 0x10000) { *out++= (val >> 12) | 0xE0;  bits=  6;}
	else if (val < 0x110000)  { *out++= (val >> 18) | 0xF0;  bits=  12; }
	else {
#ifdef FUZZING_BUILD_MODE_UNSAFE_FOR_PRODUCTION
            xmlAbort("xmlCopyCharMultiByte: codepoint out of range\n");
#endif
	    return(0);
	}
	for ( ; bits >= 0; bits-= 6)
	    *out++= ((val >> bits) & 0x3F) | 0x80 ;
	return (out - savedout);
    }
    *out = val;
    return 1;
}

/**
 * 在数组中追加字符值
 *
 * @deprecated 请勿使用。
 *
 * @param len  忽略，兼容性
 * @param out  指向 xmlChar 数组的指针
 * @param val  字符值
 * @returns 写入的 xmlChar 数量
 */

int
xmlCopyChar(int len ATTRIBUTE_UNUSED, xmlChar *out, int val) {
    if ((out == NULL) || (val < 0)) return(0);
    /* len 参数被忽略 */
    if  (val >= 0x80) {
	return(xmlCopyCharMultiByte (out, val));
    }
    *out = val;
    return 1;
}

/************************************************************************
 *									*
 *		切换编码的便利函数					*
 *									*
 ************************************************************************/

/**
 * 安装自定义实现以在字符编码之间转换。
 *
 * 这绕过了全局编码处理程序或编码别名等遗留功能。
 *
 * @since 2.14.0
 * @param ctxt  解析器上下文
 * @param impl  回调
 * @param vctxt  用户数据
 */
void
xmlCtxtSetCharEncConvImpl(xmlParserCtxtPtr ctxt, xmlCharEncConvImpl impl,
                          void *vctxt) {
    if (ctxt == NULL)
        return;

    ctxt->convImpl = impl;
    ctxt->convCtxt = vctxt;
}

static xmlParserErrors
xmlDetectEBCDIC(xmlParserCtxtPtr ctxt, xmlCharEncodingHandlerPtr *hout) {
    xmlChar out[200];
    xmlParserInputPtr input = ctxt->input;
    xmlCharEncodingHandlerPtr handler;
    int inlen, outlen, i;
    xmlParserErrors code;
    xmlCharEncError res;

    *hout = NULL;

    /*
     * 为了检测 EBCDIC 代码页，我们将前 200 字节转换为 IBM037 (EBCDIC-US)
     * 并尝试找到编码声明。
     */
    code = xmlCreateCharEncodingHandler("IBM037", XML_ENC_INPUT,
            ctxt->convImpl, ctxt->convCtxt, &handler);
    if (code != XML_ERR_OK)
        return(code);
    outlen = sizeof(out) - 1;
    inlen = input->end - input->cur;
    res = xmlEncInputChunk(handler, out, &outlen, input->cur, &inlen,
                           /* flush */ 0);
    /*
     * 如果解码失败，返回 EBCDIC 处理程序。错误将稍后报告。
     */
    if (res < 0)
        goto done;
    out[outlen] = 0;

    for (i = 0; i < outlen; i++) {
        if (out[i] == '>')
            break;
        if ((out[i] == 'e') &&
            (xmlStrncmp(out + i, BAD_CAST "encoding", 8) == 0)) {
            int start, cur, quote;

            i += 8;
            while (IS_BLANK_CH(out[i]))
                i += 1;
            if (out[i++] != '=')
                break;
            while (IS_BLANK_CH(out[i]))
                i += 1;
            quote = out[i++];
            if ((quote != '\'') && (quote != '"'))
                break;
            start = i;
            cur = out[i];
            while (((cur >= 'a') && (cur <= 'z')) ||
                   ((cur >= 'A') && (cur <= 'Z')) ||
                   ((cur >= '0') && (cur <= '9')) ||
                   (cur == '.') || (cur == '_') ||
                   (cur == '-'))
                cur = out[++i];
            if (cur != quote)
                break;
            out[i] = 0;
            xmlCharEncCloseFunc(handler);
            code = xmlCreateCharEncodingHandler((char *) out + start,
                    XML_ENC_INPUT, ctxt->convImpl, ctxt->convCtxt,
                    &handler);
            if (code != XML_ERR_OK)
                return(code);
            *hout = handler;
            return(XML_ERR_OK);
        }
    }

done:
    /*
     * 编码处理程序是有状态的，所以我们必须重新创建它们。
     */
    xmlCharEncCloseFunc(handler);
    code = xmlCreateCharEncodingHandler("IBM037", XML_ENC_INPUT,
            ctxt->convImpl, ctxt->convCtxt, &handler);
    if (code != XML_ERR_OK)
        return(code);
    *hout = handler;
    return(XML_ERR_OK);
}

/**
 * 使用枚举指定的编码来解码输入数据。这会覆盖在 XML 声明中找到的编码。
 *
 * 此函数也可用于覆盖传递给 xmlParseChunk() 的块的编码。
 *
 * @param ctxt  解析器上下文
 * @param enc  编码值（数字）
 * @returns 成功时返回 0，否则返回 -1
 */
int
xmlSwitchEncoding(xmlParserCtxtPtr ctxt, xmlCharEncoding enc)
{
    xmlCharEncodingHandlerPtr handler = NULL;
    int ret;
    xmlParserErrors code;

    if ((ctxt == NULL) || (ctxt->input == NULL))
        return(-1);

    code = xmlLookupCharEncodingHandler(enc, &handler);
    if (code != 0) {
        xmlFatalErr(ctxt, code, NULL);
        return(-1);
    }

    ret = xmlSwitchToEncoding(ctxt, handler);

    if ((ret >= 0) && (enc == XML_CHAR_ENCODING_NONE)) {
        ctxt->input->flags &= ~XML_INPUT_HAS_ENCODING;
    }

    return(ret);
}

/**
 * @param ctxt  解析器上下文
 * @param input  输入流
 * @param encoding  编码名称
 * @returns 成功时返回 0，否则返回 -1
 */
static int
xmlSwitchInputEncodingName(xmlParserCtxtPtr ctxt, xmlParserInputPtr input,
                           const char *encoding) {
    xmlCharEncodingHandlerPtr handler;
    xmlParserErrors res;

    if (encoding == NULL)
        return(-1);

    res = xmlCreateCharEncodingHandler(encoding, XML_ENC_INPUT,
            ctxt->convImpl, ctxt->convCtxt, &handler);
    if (res == XML_ERR_UNSUPPORTED_ENCODING) {
        xmlWarningMsg(ctxt, XML_ERR_UNSUPPORTED_ENCODING,
                      "Unsupported encoding: %s\n", BAD_CAST encoding, NULL);
        return(-1);
    } else if (res != XML_ERR_OK) {
        xmlFatalErr(ctxt, res, encoding);
        return(-1);
    }

    res  = xmlInputSetEncodingHandler(input, handler);
    if (res != XML_ERR_OK) {
        xmlCtxtErrIO(ctxt, res, NULL);
        return(-1);
    }

    return(0);
}

/**
 * 使用指定的编码来解码输入数据。这会覆盖在 XML 声明中找到的编码。
 *
 * 此函数也可用于覆盖传递给 xmlParseChunk() 的块的编码。
 *
 * @since 2.13.0
 *
 * @param ctxt  解析器上下文
 * @param encoding  编码名称
 * @returns 成功时返回 0，否则返回 -1
 */
int
xmlSwitchEncodingName(xmlParserCtxtPtr ctxt, const char *encoding) {
    if (ctxt == NULL)
        return(-1);

    return(xmlSwitchInputEncodingName(ctxt, ctxt->input, encoding));
}

/**
 * 使用编码处理程序来解码输入数据。
 *
 * 在出错时关闭处理程序。
 *
 * @param input  输入流
 * @param handler  编码处理程序
 * @returns xmlParserErrors 代码。
 */
xmlParserErrors
xmlInputSetEncodingHandler(xmlParserInputPtr input,
                           xmlCharEncodingHandlerPtr handler) {
    xmlParserInputBufferPtr in;
    xmlBufPtr buf;
    xmlParserErrors code = XML_ERR_OK;

    if ((input == NULL) || (input->buf == NULL)) {
        xmlCharEncCloseFunc(handler);
	return(XML_ERR_ARGUMENT);
    }
    in = input->buf;

    input->flags |= XML_INPUT_HAS_ENCODING;

    /*
     * UTF-8 不需要编码处理程序。
     */
    if ((handler != NULL) &&
        (xmlStrcasecmp(BAD_CAST handler->name, BAD_CAST "UTF-8") == 0)) {
        xmlCharEncCloseFunc(handler);
        handler = NULL;
    }

    if (in->encoder == handler)
        return(XML_ERR_OK);

    if (in->encoder != NULL) {
        /*
         * 在解析过程中切换编码是一个非常糟糕的想法，
         * 但 Chromium 可以在单独调用 xmlParseChunk 之前在 ISO-8859-1 和 UTF-16 之间切换。
         *
         * TODO: 我们应该检查 "raw" 输入缓冲区是否为空，
         * 并使用旧编码器转换旧内容。
         */

        xmlCharEncCloseFunc(in->encoder);
        in->encoder = handler;
        return(XML_ERR_OK);
    }

    buf = xmlBufCreate(XML_IO_BUFFER_SIZE);
    if (buf == NULL) {
        xmlCharEncCloseFunc(handler);
        return(XML_ERR_NO_MEMORY);
    }

    in->encoder = handler;
    in->raw = in->buffer;
    in->buffer = buf;

    /*
     * 管道中是否已经有一些内容需要转换？
     */
    if (input->end > input->base) {
        size_t processed;
        size_t nbchars;
        xmlCharEncError res;

        /*
         * 收缩当前输入缓冲区。
         * 将其移动为原始缓冲区并创建新的输入缓冲区
         */
        processed = input->cur - input->base;
        xmlBufShrink(in->raw, processed);
        input->consumed += processed;
        in->rawconsumed = processed;

        /*
         * 如果我们正在进行推送解析，我们必须转换整个缓冲区。
         *
         * 如果我们正在进行拉取解析，我们可能正在从一个巨大的内存缓冲区解析，
         * 我们不想完全转换它。
         */
        if (input->flags & XML_INPUT_PROGRESSIVE)
            nbchars = SIZE_MAX;
        else
            nbchars = 4000 /* MINLEN */;
        res = xmlCharEncInput(in, &nbchars, /* flush */ 0);
        if (res != XML_ENC_ERR_SUCCESS)
            code = in->error;
    }

    xmlBufResetInput(in->buffer, input);

    return(code);
}

/**
 * 使用编码处理程序来解码输入数据。
 *
 * @deprecated 内部函数，请勿使用。
 *
 * @param ctxt  解析器上下文，仅用于错误报告
 * @param input  输入流
 * @param handler  编码处理程序
 * @returns 成功时返回 0，否则返回 -1
 */
int
xmlSwitchInputEncoding(xmlParserCtxtPtr ctxt, xmlParserInputPtr input,
                       xmlCharEncodingHandlerPtr handler) {
    xmlParserErrors code = xmlInputSetEncodingHandler(input, handler);

    if (code != XML_ERR_OK) {
        xmlCtxtErrIO(ctxt, code, NULL);
        return(-1);
    }

    return(0);
}

/**
 * 使用编码处理程序来解码输入数据。
 *
 * 此函数可用于强制传递给 xmlParseChunk() 的块的编码。
 *
 * @param ctxt  解析器上下文
 * @param handler  编码处理程序
 * @returns 成功时返回 0，否则返回 -1
 */
int
xmlSwitchToEncoding(xmlParserCtxtPtr ctxt, xmlCharEncodingHandlerPtr handler)
{
    xmlParserErrors code;

    if (ctxt == NULL)
        return(-1);

    code = xmlInputSetEncodingHandler(ctxt->input, handler);
    if (code != XML_ERR_OK) {
        xmlCtxtErrIO(ctxt, code, NULL);
        return(-1);
    }

    return(0);
}

/**
 * 处理可选的 BOM，检测并切换到编码。
 *
 * 假设输入缓冲区中至少有四个字节。
 * @param ctxt  解析器上下文
 */
void
xmlDetectEncoding(xmlParserCtxtPtr ctxt) {
    const xmlChar *in;
    xmlCharEncoding enc;
    int bomSize;
    int autoFlag = 0;

    if (xmlParserGrow(ctxt) < 0)
        return;
    in = ctxt->input->cur;
    if (ctxt->input->end - in < 4)
        return;

    if (ctxt->input->flags & XML_INPUT_HAS_ENCODING) {
        /*
         * 如果编码已经设置，只跳过可能被解码为 UTF-8 的 BOM。
         */
        if ((in[0] == 0xEF) && (in[1] == 0xBB) && (in[2] == 0xBF)) {
            ctxt->input->cur += 3;
        }

        return;
    }

    enc = XML_CHAR_ENCODING_NONE;
    bomSize = 0;

    /*
     * BOM 嗅探和 XML 声明初始字节的检测。
     *
     * HTML5 规范不涵盖 UTF-32 (UCS-4) 或 EBCDIC。
     */
    switch (in[0]) {
        case 0x00:
            if ((!ctxt->html) &&
                (in[1] == 0x00) && (in[2] == 0x00) && (in[3] == 0x3C)) {
                enc = XML_CHAR_ENCODING_UCS4BE;
                autoFlag = XML_INPUT_AUTO_OTHER;
            } else if ((in[1] == 0x3C) && (in[2] == 0x00) && (in[3] == 0x3F)) {
                /*
                 * TODO: HTML5 规范要求检查下一个代码点是否为 'x'。
                 */
                enc = XML_CHAR_ENCODING_UTF16BE;
                autoFlag = XML_INPUT_AUTO_UTF16BE;
            }
            break;

        case 0x3C:
            if (in[1] == 0x00) {
                if ((!ctxt->html) &&
                    (in[2] == 0x00) && (in[3] == 0x00)) {
                    enc = XML_CHAR_ENCODING_UCS4LE;
                    autoFlag = XML_INPUT_AUTO_OTHER;
                } else if ((in[2] == 0x3F) && (in[3] == 0x00)) {
                    /*
                     * TODO: HTML5 规范要求检查下一个代码点是否为 'x'。
                     */
                    enc = XML_CHAR_ENCODING_UTF16LE;
                    autoFlag = XML_INPUT_AUTO_UTF16LE;
                }
            }
            break;

        case 0x4C:
	    if ((!ctxt->html) &&
                (in[1] == 0x6F) && (in[2] == 0xA7) && (in[3] == 0x94)) {
	        enc = XML_CHAR_ENCODING_EBCDIC;
                autoFlag = XML_INPUT_AUTO_OTHER;
            }
            break;

        case 0xEF:
            if ((in[1] == 0xBB) && (in[2] == 0xBF)) {
                enc = XML_CHAR_ENCODING_UTF8;
                autoFlag = XML_INPUT_AUTO_UTF8;
                bomSize = 3;
            }
            break;

        case 0xFE:
            if (in[1] == 0xFF) {
                enc = XML_CHAR_ENCODING_UTF16BE;
                autoFlag = XML_INPUT_AUTO_UTF16BE;
                bomSize = 2;
            }
            break;

        case 0xFF:
            if (in[1] == 0xFE) {
                enc = XML_CHAR_ENCODING_UTF16LE;
                autoFlag = XML_INPUT_AUTO_UTF16LE;
                bomSize = 2;
            }
            break;
    }

    if (bomSize > 0) {
        ctxt->input->cur += bomSize;
    }

    if (enc != XML_CHAR_ENCODING_NONE) {
        ctxt->input->flags |= autoFlag;

        if (enc == XML_CHAR_ENCODING_EBCDIC) {
            xmlCharEncodingHandlerPtr handler;
            xmlParserErrors res;

            res = xmlDetectEBCDIC(ctxt, &handler);
            if (res != XML_ERR_OK) {
                xmlFatalErr(ctxt, res, "detecting EBCDIC\n");
            } else {
                xmlSwitchToEncoding(ctxt, handler);
            }
        } else {
            xmlSwitchEncoding(ctxt, enc);
        }
    }
}

/**
 * 从文档中的声明设置编码。
 *
 * 如果尚未设置编码，则切换编码。否则，只警告编码不匹配。
 *
 * 获取 'encoding' 的所有权。
 * @param ctxt  解析器上下文
 * @param encoding  声明的编码
 */
void
xmlSetDeclaredEncoding(xmlParserCtxtPtr ctxt, xmlChar *encoding) {
    if (((ctxt->input->flags & XML_INPUT_HAS_ENCODING) == 0) &&
        ((ctxt->options & XML_PARSE_IGNORE_ENC) == 0)) {
        xmlCharEncodingHandlerPtr handler;
        xmlParserErrors res;

        /*
         * xmlSwitchEncodingName 将不支持的编码视为警告，
         * 但我们希望在编码声明中将其视为错误。
         */
        res = xmlCreateCharEncodingHandler((const char *) encoding,
                XML_ENC_INPUT, ctxt->convImpl, ctxt->convCtxt, &handler);
        if (res != XML_ERR_OK) {
            xmlFatalErr(ctxt, res, (const char *) encoding);
            xmlFree(encoding);
            return;
        }

        res  = xmlInputSetEncodingHandler(ctxt->input, handler);
        if (res != XML_ERR_OK) {
            xmlCtxtErrIO(ctxt, res, NULL);
            xmlFree(encoding);
            return;
        }

        ctxt->input->flags |= XML_INPUT_USES_ENC_DECL;
    } else if (ctxt->input->flags & XML_INPUT_AUTO_ENCODING) {
        static const char *allowedUTF8[] = {
            "UTF-8", "UTF8", NULL
        };
        static const char *allowedUTF16LE[] = {
            "UTF-16", "UTF-16LE", "UTF16", NULL
        };
        static const char *allowedUTF16BE[] = {
            "UTF-16", "UTF-16BE", "UTF16", NULL
        };
        const char **allowed = NULL;
        const char *autoEnc = NULL;

        switch (ctxt->input->flags & XML_INPUT_AUTO_ENCODING) {
            case XML_INPUT_AUTO_UTF8:
                allowed = allowedUTF8;
                autoEnc = "UTF-8";
                break;
            case XML_INPUT_AUTO_UTF16LE:
                allowed = allowedUTF16LE;
                autoEnc = "UTF-16LE";
                break;
            case XML_INPUT_AUTO_UTF16BE:
                allowed = allowedUTF16BE;
                autoEnc = "UTF-16BE";
                break;
        }

        if (allowed != NULL) {
            const char **p;
            int match = 0;

            for (p = allowed; *p != NULL; p++) {
                if (xmlStrcasecmp(encoding, BAD_CAST *p) == 0) {
                    match = 1;
                    break;
                }
            }

            if (match == 0) {
                xmlWarningMsg(ctxt, XML_WAR_ENCODING_MISMATCH,
                              "Encoding '%s' doesn't match "
                              "auto-detected '%s'\n",
                              encoding, BAD_CAST autoEnc);
                xmlFree(encoding);
                encoding = xmlStrdup(BAD_CAST autoEnc);
                if (encoding == NULL)
                    xmlCtxtErrMemory(ctxt);
            }
        }
    }

    if (ctxt->encoding != NULL)
        xmlFree((xmlChar *) ctxt->encoding);
    ctxt->encoding = encoding;
}

/**
 * @since 2.14.0
 *
 * @param ctxt  解析器上下文
 * @returns 来自编码声明的编码。这可能与实际编码不同。
 */
const xmlChar *
xmlCtxtGetDeclaredEncoding(xmlParserCtxtPtr ctxt) {
    if (ctxt == NULL)
        return(NULL);

    return(ctxt->encoding);
}

/**
 * @param ctxt  解析器上下文
 * @returns 用于解析文档的实际编码。这可能与声明的编码不同。
 */
const xmlChar *
xmlGetActualEncoding(xmlParserCtxtPtr ctxt) {
    const xmlChar *encoding = NULL;

    if ((ctxt->input->flags & XML_INPUT_USES_ENC_DECL) ||
        (ctxt->input->flags & XML_INPUT_AUTO_ENCODING)) {
        /* 精确保留编码 */
        encoding = ctxt->encoding;
    } else if ((ctxt->input->buf) && (ctxt->input->buf->encoder)) {
        encoding = BAD_CAST ctxt->input->buf->encoder->name;
    } else if (ctxt->input->flags & XML_INPUT_HAS_ENCODING) {
        encoding = BAD_CAST "UTF-8";
    }

    return(encoding);
}

/************************************************************************
 *									*
 *	处理实体处理的便利函数					*
 *									*
 ************************************************************************/

/**
 * 释放输入流。
 *
 * @param input  xmlParserInputPtr
 */
void
xmlFreeInputStream(xmlParserInputPtr input) {
    if (input == NULL) return;

    if (input->filename != NULL) xmlFree((char *) input->filename);
    if (input->version != NULL) xmlFree((char *) input->version);
    if ((input->free != NULL) && (input->base != NULL))
        input->free((xmlChar *) input->base);
    if (input->buf != NULL)
        xmlFreeParserInputBuffer(input->buf);
    xmlFree(input);
}

/**
 * 创建新的输入流结构。
 *
 * @deprecated 使用 xmlNewInputFromUrl() 或类似函数。
 *
 * @param ctxt  XML 解析器上下文
 * @returns 新的输入流或 NULL
 */
xmlParserInputPtr
xmlNewInputStream(xmlParserCtxtPtr ctxt) {
    xmlParserInputPtr input;

    input = (xmlParserInputPtr) xmlMalloc(sizeof(xmlParserInput));
    if (input == NULL) {
        xmlCtxtErrMemory(ctxt);
	return(NULL);
    }
    memset(input, 0, sizeof(xmlParserInput));
    input->line = 1;
    input->col = 1;

    return(input);
}

/**
 * 从文件系统、网络或用户定义的资源加载器创建新的解析器输入。
 *
 * @param ctxt  解析器上下文
 * @param url  文件名或 URL
 * @param publicId  来自 doctype 的公共 ID（可选）
 * @param encoding  字符编码（可选）
 * @param flags  未使用，传递 0
 * @returns 新的解析器输入。
 */
xmlParserInputPtr
xmlCtxtNewInputFromUrl(xmlParserCtxtPtr ctxt, const char *url,
                       const char *publicId, const char *encoding,
                       xmlParserInputFlags flags ATTRIBUTE_UNUSED) {
    xmlParserInputPtr input;

    if ((ctxt == NULL) || (url == NULL))
	return(NULL);

    input = xmlLoadResource(ctxt, url, publicId, XML_RESOURCE_MAIN_DOCUMENT);
    if (input == NULL)
        return(NULL);

    if (encoding != NULL)
        xmlSwitchInputEncodingName(ctxt, input, encoding);

    return(input);
}

/**
 * 内部辅助函数。
 *
 * @param buf  解析器输入缓冲区
 * @param filename  文件名或 URL
 * @returns 新的解析器输入。
 */
static xmlParserInputPtr
xmlNewInputInternal(xmlParserInputBufferPtr buf, const char *filename) {
    xmlParserInputPtr input;

    input = (xmlParserInputPtr) xmlMalloc(sizeof(xmlParserInput));
    if (input == NULL) {
	xmlFreeParserInputBuffer(buf);
	return(NULL);
    }
    memset(input, 0, sizeof(xmlParserInput));
    input->line = 1;
    input->col = 1;

    input->buf = buf;
    xmlBufResetInput(input->buf->buffer, input);

    if (filename != NULL) {
        input->filename = xmlMemStrdup(filename);
        if (input->filename == NULL) {
            xmlFreeInputStream(input);
            return(NULL);
        }
    }

    return(input);
}

/**
 * 创建新的解析器输入以从内存区域读取。
 *
 * `url` 用作解析外部实体和错误报告的基础。
 *
 * 如果设置了 XML_INPUT_BUF_STATIC 标志，内存区域必须在解析完成之前保持不变。
 * 这可以避免临时复制。
 *
 * 如果设置了 XML_INPUT_BUF_ZERO_TERMINATED 标志，内存区域必须在位置 `size`
 * 的缓冲区后包含一个零字节。这可以避免临时复制。
 *
 * @since 2.14.0
 *
 * @param url  基础 URL（可选）
 * @param mem  指向字符数组的指针
 * @param size  数组大小
 * @param flags  优化提示
 * @returns 新的解析器输入，如果内存分配失败则返回 NULL。
 */
xmlParserInputPtr
xmlNewInputFromMemory(const char *url, const void *mem, size_t size,
                      xmlParserInputFlags flags) {
    xmlParserInputBufferPtr buf;

    if (mem == NULL)
	return(NULL);

    buf = xmlNewInputBufferMemory(mem, size, flags, XML_CHAR_ENCODING_NONE);
    if (buf == NULL)
        return(NULL);

    return(xmlNewInputInternal(buf, url));
}

/**
 * @param ctxt  解析器上下文
 * @param url  基础 URL（可选）
 * @param mem  指向字符数组的指针
 * @param size  数组大小
 * @param encoding  字符编码（可选）
 * @param flags  优化提示
 * @returns 新的解析器输入，出错时返回 NULL。
 */
xmlParserInputPtr
xmlCtxtNewInputFromMemory(xmlParserCtxtPtr ctxt, const char *url,
                          const void *mem, size_t size,
                          const char *encoding, xmlParserInputFlags flags) {
    xmlParserInputPtr input;

    if ((ctxt == NULL) || (mem == NULL))
	return(NULL);

    input = xmlNewInputFromMemory(url, mem, size, flags);
    if (input == NULL) {
        xmlCtxtErrMemory(ctxt);
        return(NULL);
    }

    if (encoding != NULL)
        xmlSwitchInputEncodingName(ctxt, input, encoding);

    return(input);
}

/**
 * 创建新的解析器输入以从零终止字符串读取。
 *
 * `url` 用作解析外部实体和错误报告的基础。
 *
 * 如果设置了 XML_INPUT_BUF_STATIC 标志，字符串必须在解析完成之前保持不变。
 * 这可以避免临时复制。
 *
 * @since 2.14.0
 *
 * @param url  基础 URL（可选）
 * @param str  零终止字符串
 * @param flags  优化提示
 * @returns 新的解析器输入，如果内存分配失败则返回 NULL。
 */
xmlParserInputPtr
xmlNewInputFromString(const char *url, const char *str,
                      xmlParserInputFlags flags) {
    xmlParserInputBufferPtr buf;

    if (str == NULL)
	return(NULL);

    buf = xmlNewInputBufferString(str, flags);
    if (buf == NULL)
        return(NULL);

    return(xmlNewInputInternal(buf, url));
}

/**
 * @param ctxt  解析器上下文
 * @param url  基础 URL（可选）
 * @param str  零终止字符串
 * @param encoding  字符编码（可选）
 * @param flags  优化提示
 * @returns 新的解析器输入。
 */
xmlParserInputPtr
xmlCtxtNewInputFromString(xmlParserCtxtPtr ctxt, const char *url,
                          const char *str, const char *encoding,
                          xmlParserInputFlags flags) {
    xmlParserInputPtr input;

    if ((ctxt == NULL) || (str == NULL))
	return(NULL);

    input = xmlNewInputFromString(url, str, flags);
    if (input == NULL) {
        xmlCtxtErrMemory(ctxt);
        return(NULL);
    }

    if (encoding != NULL)
        xmlSwitchInputEncodingName(ctxt, input, encoding);

    return(input);
}

/**
 * 创建新的解析器输入以从文件描述符读取。
 *
 * `url` 用作解析外部实体和错误报告的基础。
 *
 * `fd` 在解析完成后关闭。
 *
 * 支持的 `flags` 是 XML_INPUT_UNZIP 用于自动解压数据。
 * 此功能已弃用，将在未来版本中删除。
 *
 * @since 2.14.0
 *
 * @param url  基础 URL（可选）
 * @param fd  文件描述符
 * @param flags  输入标志
 * @returns 新的解析器输入，如果内存分配失败则返回 NULL。
 */
xmlParserInputPtr
xmlNewInputFromFd(const char *url, int fd, xmlParserInputFlags flags) {
    xmlParserInputBufferPtr buf;

    if (fd < 0)
	return(NULL);

    buf = xmlAllocParserInputBuffer(XML_CHAR_ENCODING_NONE);
    if (buf == NULL)
        return(NULL);

    if (xmlInputFromFd(buf, fd, flags) != XML_ERR_OK) {
        xmlFreeParserInputBuffer(buf);
        return(NULL);
    }

    return(xmlNewInputInternal(buf, url));
}

/**
 * @param ctxt  解析器上下文
 * @param url  基础 URL（可选）
 * @param fd  文件描述符
 * @param encoding  字符编码（可选）
 * @param flags  未使用，传递 0
 * @returns 新的解析器输入。
 */
xmlParserInputPtr
xmlCtxtNewInputFromFd(xmlParserCtxtPtr ctxt, const char *url,
                      int fd, const char *encoding,
                      xmlParserInputFlags flags) {
    xmlParserInputPtr input;

    if ((ctxt == NULL) || (fd < 0))
	return(NULL);

    if (ctxt->options & XML_PARSE_UNZIP)
        flags |= XML_INPUT_UNZIP;

    input = xmlNewInputFromFd(url, fd, flags);
    if (input == NULL) {
	xmlCtxtErrMemory(ctxt);
        return(NULL);
    }

    if (encoding != NULL)
        xmlSwitchInputEncodingName(ctxt, input, encoding);

    return(input);
}

/**
 * 创建新的解析器输入以从输入回调和上下文读取。
 *
 * `url` 用作解析外部实体和错误报告的基础。
 *
 * `ioRead` 被调用以将新数据读入提供的缓冲区。
 * 它必须返回写入缓冲区的字节数，失败时返回负的 xmlParserErrors 代码。
 *
 * `ioClose` 在解析完成后被调用。
 *
 * `ioCtxt` 是传递给回调的不透明指针。
 *
 * @since 2.14.0
 *
 * @param url  基础 URL（可选）
 * @param ioRead  读取回调
 * @param ioClose  关闭回调（可选）
 * @param ioCtxt  IO 上下文
 * @param flags  未使用，传递 0
 * @returns 新的解析器输入，如果内存分配失败则返回 NULL。
 */
xmlParserInputPtr
xmlNewInputFromIO(const char *url, xmlInputReadCallback ioRead,
                  xmlInputCloseCallback ioClose, void *ioCtxt,
                  xmlParserInputFlags flags ATTRIBUTE_UNUSED) {
    xmlParserInputBufferPtr buf;

    if (ioRead == NULL)
	return(NULL);

    buf = xmlAllocParserInputBuffer(XML_CHAR_ENCODING_NONE);
    if (buf == NULL) {
        if (ioClose != NULL)
            ioClose(ioCtxt);
        return(NULL);
    }

    buf->context = ioCtxt;
    buf->readcallback = ioRead;
    buf->closecallback = ioClose;

    return(xmlNewInputInternal(buf, url));
}

/**
 * @param ctxt  解析器上下文
 * @param url  基础 URL（可选）
 * @param ioRead  读取回调
 * @param ioClose  关闭回调（可选）
 * @param ioCtxt  IO 上下文
 * @param encoding  字符编码（可选）
 * @param flags  未使用，传递 0
 * @returns 新的解析器输入。
 */
xmlParserInputPtr
xmlCtxtNewInputFromIO(xmlParserCtxtPtr ctxt, const char *url,
                      xmlInputReadCallback ioRead,
                      xmlInputCloseCallback ioClose,
                      void *ioCtxt, const char *encoding,
                      xmlParserInputFlags flags) {
    xmlParserInputPtr input;

    if ((ctxt == NULL) || (ioRead == NULL))
	return(NULL);

    input = xmlNewInputFromIO(url, ioRead, ioClose, ioCtxt, flags);
    if (input == NULL) {
        xmlCtxtErrMemory(ctxt);
        return(NULL);
    }

    if (encoding != NULL)
        xmlSwitchInputEncodingName(ctxt, input, encoding);

    return(input);
}

/**
 * 为推送解析器创建新的解析器输入。
 *
 * @param url  基础 URL（可选）
 * @param chunk  指向字符数组的指针
 * @param size  数组大小
 * @returns 新的解析器输入，如果内存分配失败则返回 NULL。
 */
xmlParserInputPtr
xmlNewPushInput(const char *url, const char *chunk, int size) {
    xmlParserInputBufferPtr buf;
    xmlParserInputPtr input;

    buf = xmlAllocParserInputBuffer(XML_CHAR_ENCODING_NONE);
    if (buf == NULL)
        return(NULL);

    input = xmlNewInputInternal(buf, url);
    if (input == NULL)
	return(NULL);

    input->flags |= XML_INPUT_PROGRESSIVE;

    if ((size > 0) && (chunk != NULL)) {
        int res;

	res = xmlParserInputBufferPush(input->buf, size, chunk);
        xmlBufResetInput(input->buf->buffer, input);
        if (res < 0) {
            xmlFreeInputStream(input);
            return(NULL);
        }
    }

    return(input);
}

/**
 * 创建新的输入流结构，将 `input` 封装为适合解析器的流。
 *
 * @param ctxt  XML 解析器上下文
 * @param buf  输入缓冲区
 * @param enc  已知的字符集编码
 * @returns 新的输入流或 NULL
 */
xmlParserInputPtr
xmlNewIOInputStream(xmlParserCtxtPtr ctxt, xmlParserInputBufferPtr buf,
	            xmlCharEncoding enc) {
    xmlParserInputPtr input;
    const char *encoding;

    if ((ctxt == NULL) || (buf == NULL))
        return(NULL);

    input = xmlNewInputInternal(buf, NULL);
    if (input == NULL) {
        xmlCtxtErrMemory(ctxt);
	return(NULL);
    }

    encoding = xmlGetCharEncodingName(enc);
    if (encoding != NULL)
        xmlSwitchInputEncodingName(ctxt, input, encoding);

    return(input);
}

/**
 * 基于 xmlEntityPtr 创建新的输入流
 *
 * @deprecated 内部函数，请勿使用。
 *
 * @param ctxt  XML 解析器上下文
 * @param ent  实体指针
 * @returns 新的输入流或 NULL
 */
xmlParserInputPtr
xmlNewEntityInputStream(xmlParserCtxtPtr ctxt, xmlEntityPtr ent) {
    xmlParserInputPtr input;

    if ((ctxt == NULL) || (ent == NULL))
	return(NULL);

    if (ent->content != NULL) {
        input = xmlCtxtNewInputFromString(ctxt, NULL,
                (const char *) ent->content, NULL, XML_INPUT_BUF_STATIC);
    } else if (ent->URI != NULL) {
        xmlResourceType rtype;

        if (ent->etype == XML_EXTERNAL_PARAMETER_ENTITY)
            rtype = XML_RESOURCE_PARAMETER_ENTITY;
        else
            rtype = XML_RESOURCE_GENERAL_ENTITY;

        input = xmlLoadResource(ctxt, (char *) ent->URI,
                                (char *) ent->ExternalID, rtype);
    } else {
        return(NULL);
    }

    if (input == NULL)
        return(NULL);

    input->entity = ent;

    return(input);
}

/**
 * 基于内存缓冲区创建新的输入流。
 *
 * @deprecated 使用 xmlNewInputFromString()。
 *
 * @param ctxt  XML 解析器上下文
 * @param buffer  内存缓冲区
 * @returns 新的输入流
 */
xmlParserInputPtr
xmlNewStringInputStream(xmlParserCtxtPtr ctxt, const xmlChar *buffer) {
    return(xmlCtxtNewInputFromString(ctxt, NULL, (const char *) buffer,
                                     NULL, 0));
}


/****************************************************************
 *								*
 *		外部实体加载					*
 *								*
 ****************************************************************/

#ifdef LIBXML_CATALOG_ENABLED

/**
 * 根据适当的目录解析 URL 和 ID。
 * 此函数由 xmlDefaultExternalEntityLoader() 和
 * xmlNoNetExternalEntityLoader() 使用。
 *
 * @param URL  要加载的实体的 URL
 * @param ID  要加载的实体的系统 ID
 * @param ctxt  调用实体的上下文或 NULL
 * @returns 新分配的 URL，或 NULL。
 */
static xmlChar *
xmlResolveResourceFromCatalog(const char *URL, const char *ID,
                              xmlParserCtxtPtr ctxt) {
    xmlChar *resource = NULL;
    xmlCatalogAllow pref;
    int allowLocal = 0;
    int allowGlobal = 0;

    /*
     * HTML 文档的加载不应使用 XML 目录。
     */
    if ((ctxt != NULL) && (ctxt->html))
        return(NULL);

    /*
     * 如果资源不作为文件存在，
     * 尝试从目录中指向的资源加载它
     */
    pref = xmlCatalogGetDefaults();

    if ((ctxt != NULL) && (ctxt->catalogs != NULL) &&
        ((pref == XML_CATA_ALLOW_ALL) ||
         (pref == XML_CATA_ALLOW_DOCUMENT)))
        allowLocal = 1;

    if (((ctxt == NULL) ||
         ((ctxt->options & XML_PARSE_NO_SYS_CATALOG) == 0)) &&
        ((pref == XML_CATA_ALLOW_ALL) ||
         (pref == XML_CATA_ALLOW_GLOBAL)))
        allowGlobal = 1;

    if ((pref != XML_CATA_ALLOW_NONE) && (!xmlNoNetExists(URL))) {
	/*
	 * 进行本地查找
	 */
        if (allowLocal) {
	    resource = xmlCatalogLocalResolve(ctxt->catalogs,
					      (const xmlChar *)ID,
					      (const xmlChar *)URL);
        }
	/*
	 * 尝试全局查找
	 */
	if ((resource == NULL) && (allowGlobal)) {
	    resource = xmlCatalogResolve((const xmlChar *)ID,
					 (const xmlChar *)URL);
	}
	if ((resource == NULL) && (URL != NULL))
	    resource = xmlStrdup((const xmlChar *) URL);

	/*
	 * TODO: 对引用进行 URI 查找
	 */
	if ((resource != NULL) && (!xmlNoNetExists((const char *)resource))) {
	    xmlChar *tmp = NULL;

	    if (allowLocal) {
		tmp = xmlCatalogLocalResolveURI(ctxt->catalogs, resource);
	    }
	    if ((tmp == NULL) && (allowGlobal)) {
		tmp = xmlCatalogResolveURI(resource);
	    }

	    if (tmp != NULL) {
		xmlFree(resource);
		resource = tmp;
	    }
	}
    }

    return resource;
}

#endif

/**
 * @deprecated 内部函数，请勿使用。
 *
 * @param ctxt  XML 解析器上下文
 * @param ret  XML 解析器输入
 * @returns NULL。
 */
xmlParserInputPtr
xmlCheckHTTPInput(xmlParserCtxtPtr ctxt ATTRIBUTE_UNUSED,
                  xmlParserInputPtr ret ATTRIBUTE_UNUSED) {
    return(NULL);
}

/**
 * 基于文件或 URL 创建新的输入流。
 *
 * 标志 XML_INPUT_UNZIP 允许解压缩。
 *
 * 标志 XML_INPUT_NETWORK 允许网络访问。
 *
 * 如果注册了以下资源加载器，将按优先级顺序调用它们：
 *
 * - 使用 xmlParserInputBufferCreateFilenameDefault() 设置的
 *   每线程 xmlParserInputBufferCreateFilenameFunc()（已弃用）
 * - 默认加载器，它将返回
 *   - 使用 xmlRegisterInputCallbacks() 设置的匹配全局输入回调的结果（已弃用）
 *   - 从文件系统打开的文件，如果编译了支持，则自动检测压缩文件。
 *
 * @since 2.14.0
 *
 * @param filename  用作实体的文件名
 * @param flags  XML_INPUT 标志
 * @param out  指向新解析器输入的指针
 * @returns xmlParserErrors 代码。
 */
xmlParserErrors
xmlNewInputFromUrl(const char *filename, xmlParserInputFlags flags,
                   xmlParserInputPtr *out) {
    xmlParserInputBufferPtr buf;
    xmlParserInputPtr input;
    xmlParserErrors code = XML_ERR_OK;

    if (out == NULL)
        return(XML_ERR_ARGUMENT);
    *out = NULL;
    if (filename == NULL)
        return(XML_ERR_ARGUMENT);

    if (xmlParserInputBufferCreateFilenameValue != NULL) {
        buf = xmlParserInputBufferCreateFilenameValue(filename,
                XML_CHAR_ENCODING_NONE);
        if (buf == NULL)
            code = XML_IO_ENOENT;
    } else {
        code = xmlParserInputBufferCreateUrl(filename, XML_CHAR_ENCODING_NONE,
                                             flags, &buf);
    }
    if (code != XML_ERR_OK)
	return(code);

    input = xmlNewInputInternal(buf, filename);
    if (input == NULL)
	return(XML_ERR_NO_MEMORY);

    *out = input;
    return(XML_ERR_OK);
}

/**
 * 基于文件或 URL 创建新的输入流。
 *
 * @deprecated 使用 xmlNewInputFromUrl()。
 *
 * @param ctxt  XML 解析器上下文
 * @param filename  用作实体的文件名
 * @returns 新的输入流，出错时返回 NULL
 */
xmlParserInputPtr
xmlNewInputFromFile(xmlParserCtxtPtr ctxt, const char *filename) {
    xmlParserInputPtr input;
    xmlParserInputFlags flags = 0;
    xmlParserErrors code;

    if ((ctxt == NULL) || (filename == NULL))
        return(NULL);

    if (ctxt->options & XML_PARSE_UNZIP)
        flags |= XML_INPUT_UNZIP;
    if ((ctxt->options & XML_PARSE_NONET) == 0)
        flags |= XML_INPUT_NETWORK;

    code = xmlNewInputFromUrl(filename, flags, &input);
    if (code != XML_ERR_OK) {
        xmlCtxtErrIO(ctxt, code, filename);
        return(NULL);
    }

    return(input);
}

/**
 * 默认情况下我们还不加载外部实体。
 *
 * @param url  要加载的实体的 URL
 * @param ID  要加载的实体的系统 ID
 * @param ctxt  调用实体的上下文或 NULL
 * @returns 新分配的 xmlParserInputPtr，或 NULL。
 */
static xmlParserInputPtr
xmlDefaultExternalEntityLoader(const char *url, const char *ID,
                               xmlParserCtxtPtr ctxt)
{
    xmlParserInputPtr input = NULL;
    char *resource = NULL;

    (void) ID;

    if (url == NULL)
        return(NULL);

#ifdef LIBXML_CATALOG_ENABLED
    resource = (char *) xmlResolveResourceFromCatalog(url, ID, ctxt);
    if (resource != NULL)
	url = resource;
#endif

    /*
     * 当传递 http URI 并设置 NONET 时，几个下游测试套件期望此错误。
     */
    if ((ctxt != NULL) &&
        (ctxt->options & XML_PARSE_NONET) &&
        (xmlStrncasecmp(BAD_CAST url, BAD_CAST "http://", 7) == 0)) {
        xmlCtxtErrIO(ctxt, XML_IO_NETWORK_ATTEMPT, url);
    } else {
        input = xmlNewInputFromFile(ctxt, url);
    }

    if (resource != NULL)
	xmlFree(resource);
    return(input);
}

/**
 * 禁用网络访问的特定实体加载器，但仍允许本地目录访问以进行解析。
 *
 * @deprecated 使用 XML_PARSE_NONET。
 *
 * @param URL  要加载的实体的 URL
 * @param ID  要加载的实体的系统 ID
 * @param ctxt  调用实体的上下文或 NULL
 * @returns 新分配的 xmlParserInputPtr，或 NULL。
 */
xmlParserInputPtr
xmlNoNetExternalEntityLoader(const char *URL, const char *ID,
                             xmlParserCtxtPtr ctxt) {
    int oldOptions = 0;
    xmlParserInputPtr input;

    if (ctxt != NULL) {
        oldOptions = ctxt->options;
        ctxt->options |= XML_PARSE_NONET;
    }

    input = xmlDefaultExternalEntityLoader(URL, ID, ctxt);

    if (ctxt != NULL)
        ctxt->options = oldOptions;

    return(input);
}

/*
 * 这个全局变量最终必须消失
 */
static xmlExternalEntityLoader
xmlCurrentExternalEntityLoader = xmlDefaultExternalEntityLoader;

/**
 * 更改应用程序的默认外部实体解析器函数。
 *
 * @deprecated 这是全局设置且不是线程安全的。使用
 * xmlCtxtSetResourceLoader() 或类似函数。
 *
 * @param f  新的实体解析器函数
 */
void
xmlSetExternalEntityLoader(xmlExternalEntityLoader f) {
    xmlCurrentExternalEntityLoader = f;
}

/**
 * 获取应用程序的默认外部实体解析器函数
 *
 * @deprecated 参见 xmlSetExternalEntityLoader()。
 *
 * @returns xmlExternalEntityLoader() 函数指针
 */
xmlExternalEntityLoader
xmlGetExternalEntityLoader(void) {
    return(xmlCurrentExternalEntityLoader);
}

/**
 * 安装自定义回调以加载文档、DTD 或外部实体。
 *
 * @since 2.14.0
 * @param ctxt  解析器上下文
 * @param loader  回调
 * @param vctxt  用户数据
 */
void
xmlCtxtSetResourceLoader(xmlParserCtxtPtr ctxt, xmlResourceLoader loader,
                         void *vctxt) {
    if (ctxt == NULL)
        return;

    ctxt->resourceLoader = loader;
    ctxt->resourceCtxt = vctxt;
}

/**
 * @param ctxt  解析器上下文
 * @param url  要加载的实体的 URL
 * @param publicId  要加载的实体的公共 ID
 * @param type  资源类型
 * @returns xmlParserInputPtr 或出错时返回 NULL。
 */
xmlParserInputPtr
xmlLoadResource(xmlParserCtxtPtr ctxt, const char *url, const char *publicId,
                xmlResourceType type) {
    char *canonicFilename;
    xmlParserInputPtr ret;

    if (url == NULL)
        return(NULL);

    if ((ctxt != NULL) && (ctxt->resourceLoader != NULL)) {
        char *resource = NULL;
        xmlParserInputFlags flags = 0;
        int code;

#ifdef LIBXML_CATALOG_ENABLED
        resource = (char *) xmlResolveResourceFromCatalog(url, publicId, ctxt);
        if (resource != NULL)
            url = resource;
#endif

        if (ctxt->options & XML_PARSE_UNZIP)
            flags |= XML_INPUT_UNZIP;
        if ((ctxt->options & XML_PARSE_NONET) == 0)
            flags |= XML_INPUT_NETWORK;

        code = ctxt->resourceLoader(ctxt->resourceCtxt, url, publicId, type,
                                    flags, &ret);
        if (code != XML_ERR_OK) {
            xmlCtxtErrIO(ctxt, code, url);
            ret = NULL;
        }
        if (resource != NULL)
            xmlFree(resource);
        return(ret);
    }

    canonicFilename = (char *) xmlCanonicPath((const xmlChar *) url);
    if (canonicFilename == NULL) {
        xmlCtxtErrMemory(ctxt);
        return(NULL);
    }

    ret = xmlCurrentExternalEntityLoader(canonicFilename, publicId, ctxt);
    xmlFree(canonicFilename);
    return(ret);
}

/**
 * `URL` is a filename or URL. If if contains the substring "://",
 * it is assumed to be a Legacy Extended IRI. Otherwise, it is
 * treated as a filesystem path.
 *
 * `ID` is an optional XML public ID, typically from a doctype
 * declaration. It is used for catalog lookups.
 *
 * If catalog lookup is enabled (default is yes) and URL or ID are
 * found in system or local XML catalogs, URL is replaced with the
 * result. Then the following resource loaders will be called if
 * they were registered (in order of precedence):
 *
 * - the resource loader set with xmlCtxtSetResourceLoader()
 * - the global external entity loader set with
 *   xmlSetExternalEntityLoader() (without catalog resolution,
 *   deprecated)
 * - the per-thread xmlParserInputBufferCreateFilenameFunc() set with
 *   xmlParserInputBufferCreateFilenameDefault() (deprecated)
 * - the default loader which will return
 *   - the result from a matching global input callback set with
 *     xmlRegisterInputCallbacks() (deprecated)
 *   - a file opened from the filesystem, with automatic detection
 *     of compressed files if support is compiled in.
 *
 * @param URL  the URL for the entity to load
 * @param ID  the Public ID for the entity to load
 * @param ctxt  the context in which the entity is called or NULL
 * @returns the xmlParserInputPtr or NULL
 */
xmlParserInputPtr
xmlLoadExternalEntity(const char *URL, const char *ID,
                      xmlParserCtxtPtr ctxt) {
    return(xmlLoadResource(ctxt, URL, ID, XML_RESOURCE_UNKNOWN));
}

/************************************************************************
 *									*
 *		处理解析器上下文的便利函数				*
 *									*
 ************************************************************************/

/**
 * 初始化 SAX 解析器上下文
 *
 * @param ctxt  XML 解析器上下文
 * @param sax  SAX 处理程序
 * @param userData  用户数据
 * @returns 成功时返回 0，出错时返回 -1
 */

static int
xmlInitSAXParserCtxt(xmlParserCtxtPtr ctxt, const xmlSAXHandler *sax,
                     void *userData)
{
    xmlParserInputPtr input;
#ifdef FUZZING_BUILD_MODE_UNSAFE_FOR_PRODUCTION
    size_t initialNodeTabSize = 1;
#else
    size_t initialNodeTabSize = 10;
#endif

    if (ctxt == NULL)
        return(-1);

    if (ctxt->dict == NULL)
	ctxt->dict = xmlDictCreate();
    if (ctxt->dict == NULL)
	return(-1);

    if (ctxt->sax == NULL)
	ctxt->sax = (xmlSAXHandler *) xmlMalloc(sizeof(xmlSAXHandler));
    if (ctxt->sax == NULL)
	return(-1);
    if (sax == NULL) {
	memset(ctxt->sax, 0, sizeof(xmlSAXHandler));
        xmlSAXVersion(ctxt->sax, 2);
        ctxt->userData = ctxt;
    } else {
	if (sax->initialized == XML_SAX2_MAGIC) {
	    memcpy(ctxt->sax, sax, sizeof(xmlSAXHandler));
        } else {
	    memset(ctxt->sax, 0, sizeof(xmlSAXHandler));
	    memcpy(ctxt->sax, sax, sizeof(xmlSAXHandlerV1));
        }
        ctxt->userData = userData ? userData : ctxt;
    }

    ctxt->maxatts = 0;
    ctxt->atts = NULL;
    /* 分配输入栈 */
    if (ctxt->inputTab == NULL) {
#ifdef FUZZING_BUILD_MODE_UNSAFE_FOR_PRODUCTION
        size_t initialSize = 1;
#else
        size_t initialSize = 5;
#endif

	ctxt->inputTab = xmlMalloc(initialSize * sizeof(xmlParserInputPtr));
	ctxt->inputMax = initialSize;
    }
    if (ctxt->inputTab == NULL)
	return(-1);
    while ((input = xmlCtxtPopInput(ctxt)) != NULL) { /* Non consuming */
        xmlFreeInputStream(input);
    }
    ctxt->inputNr = 0;
    ctxt->input = NULL;

    ctxt->version = NULL;
    ctxt->encoding = NULL;
    ctxt->standalone = -1;
    ctxt->hasExternalSubset = 0;
    ctxt->hasPErefs = 0;
    ctxt->html = 0;
    ctxt->instate = XML_PARSER_START;

    /* 分配节点栈 */
    if (ctxt->nodeTab == NULL) {
	ctxt->nodeTab = xmlMalloc(initialNodeTabSize * sizeof(xmlNodePtr));
	ctxt->nodeMax = initialNodeTabSize;
    }
    if (ctxt->nodeTab == NULL)
	return(-1);
    ctxt->nodeNr = 0;
    ctxt->node = NULL;

    /* 分配名称栈 */
    if (ctxt->nameTab == NULL) {
	ctxt->nameTab = xmlMalloc(initialNodeTabSize * sizeof(xmlChar *));
	ctxt->nameMax = initialNodeTabSize;
    }
    if (ctxt->nameTab == NULL)
	return(-1);
    ctxt->nameNr = 0;
    ctxt->name = NULL;

    /* 分配空间栈 */
    if (ctxt->spaceTab == NULL) {
	ctxt->spaceTab = xmlMalloc(initialNodeTabSize * sizeof(int));
	ctxt->spaceMax = initialNodeTabSize;
    }
    if (ctxt->spaceTab == NULL)
	return(-1);
    ctxt->spaceNr = 1;
    ctxt->spaceTab[0] = -1;
    ctxt->space = &ctxt->spaceTab[0];
    ctxt->myDoc = NULL;
    ctxt->wellFormed = 1;
    ctxt->nsWellFormed = 1;
    ctxt->valid = 1;

    ctxt->options = XML_PARSE_NODICT;

    /*
     * 从已弃用的全局变量初始化一些解析器选项。
     * 请注意，采用选项参数的"现代"API 或 xmlCtxtSetOptions 将忽略这些默认值。
     * 它们只有在使用像 xmlParseFile 这样的旧 API 函数时才相关。
     */
    ctxt->loadsubset = xmlLoadExtDtdDefaultValue;
    if (ctxt->loadsubset) {
        ctxt->options |= XML_PARSE_DTDLOAD;
    }
    ctxt->validate = xmlDoValidityCheckingDefaultValue;
    if (ctxt->validate) {
        ctxt->options |= XML_PARSE_DTDVALID;
    }
    ctxt->pedantic = xmlPedanticParserDefaultValue;
    if (ctxt->pedantic) {
        ctxt->options |= XML_PARSE_PEDANTIC;
    }
    ctxt->linenumbers = xmlLineNumbersDefaultValue;
    ctxt->keepBlanks = xmlKeepBlanksDefaultValue;
    if (ctxt->keepBlanks == 0) {
	ctxt->sax->ignorableWhitespace = xmlSAX2IgnorableWhitespace;
	ctxt->options |= XML_PARSE_NOBLANKS;
    }
    ctxt->replaceEntities = xmlSubstituteEntitiesDefaultValue;
    if (ctxt->replaceEntities) {
        ctxt->options |= XML_PARSE_NOENT;
    }
    if (xmlGetWarningsDefaultValue == 0)
        ctxt->options |= XML_PARSE_NOWARNING;

    ctxt->vctxt.flags = XML_VCTXT_USE_PCTXT;
    ctxt->vctxt.userData = ctxt;
    ctxt->vctxt.error = xmlParserValidityError;
    ctxt->vctxt.warning = xmlParserValidityWarning;

    ctxt->record_info = 0;
    ctxt->checkIndex = 0;
    ctxt->inSubset = 0;
    ctxt->errNo = XML_ERR_OK;
    ctxt->depth = 0;
    ctxt->catalogs = NULL;
    ctxt->sizeentities = 0;
    ctxt->sizeentcopy = 0;
    ctxt->input_id = 1;
    ctxt->maxAmpl = XML_MAX_AMPLIFICATION_DEFAULT;
    xmlInitNodeInfoSeq(&ctxt->node_seq);

    if (ctxt->nsdb == NULL) {
        ctxt->nsdb = xmlParserNsCreate();
        if (ctxt->nsdb == NULL)
            return(-1);
    }

    return(0);
}

/**
 * 初始化解析器上下文
 *
 * @deprecated 内部函数，将在未来版本中设为私有。
 *
 * @param ctxt  XML 解析器上下文
 * @returns 成功时返回 0，出错时返回 -1
 */

int
xmlInitParserCtxt(xmlParserCtxtPtr ctxt)
{
    return(xmlInitSAXParserCtxt(ctxt, NULL, NULL));
}

/**
 * 释放解析器上下文使用的所有内存。但是 ctxt->myDoc 中的已解析文档不会被释放。
 *
 * @param ctxt  XML 解析器上下文
 */

void
xmlFreeParserCtxt(xmlParserCtxtPtr ctxt)
{
    xmlParserInputPtr input;

    if (ctxt == NULL) return;

    while ((input = xmlCtxtPopInput(ctxt)) != NULL) { /* Non consuming */
        xmlFreeInputStream(input);
    }
    if (ctxt->spaceTab != NULL) xmlFree(ctxt->spaceTab);
    if (ctxt->nameTab != NULL) xmlFree((xmlChar * *)ctxt->nameTab);
    if (ctxt->nodeTab != NULL) xmlFree(ctxt->nodeTab);
    if (ctxt->nodeInfoTab != NULL) xmlFree(ctxt->nodeInfoTab);
    if (ctxt->inputTab != NULL) xmlFree(ctxt->inputTab);
    if (ctxt->version != NULL) xmlFree((char *) ctxt->version);
    if (ctxt->encoding != NULL) xmlFree((char *) ctxt->encoding);
    if (ctxt->extSubURI != NULL) xmlFree((char *) ctxt->extSubURI);
    if (ctxt->extSubSystem != NULL) xmlFree((char *) ctxt->extSubSystem);
#ifdef LIBXML_SAX1_ENABLED
    if ((ctxt->sax != NULL) &&
        (ctxt->sax != (xmlSAXHandlerPtr) &xmlDefaultSAXHandler))
#else
    if (ctxt->sax != NULL)
#endif /* LIBXML_SAX1_ENABLED */
        xmlFree(ctxt->sax);
    if (ctxt->directory != NULL) xmlFree(ctxt->directory);
    if (ctxt->vctxt.nodeTab != NULL) xmlFree(ctxt->vctxt.nodeTab);
    if (ctxt->atts != NULL) xmlFree((xmlChar * *)ctxt->atts);
    if (ctxt->dict != NULL) xmlDictFree(ctxt->dict);
    if (ctxt->nsTab != NULL) xmlFree(ctxt->nsTab);
    if (ctxt->nsdb != NULL) xmlParserNsFree(ctxt->nsdb);
    if (ctxt->attrHash != NULL) xmlFree(ctxt->attrHash);
    if (ctxt->pushTab != NULL) xmlFree(ctxt->pushTab);
    if (ctxt->attallocs != NULL) xmlFree(ctxt->attallocs);
    if (ctxt->attsDefault != NULL)
        xmlHashFree(ctxt->attsDefault, xmlHashDefaultDeallocator);
    if (ctxt->attsSpecial != NULL)
        xmlHashFree(ctxt->attsSpecial, NULL);
    if (ctxt->freeElems != NULL) {
        xmlNodePtr cur, next;

	cur = ctxt->freeElems;
	while (cur != NULL) {
	    next = cur->next;
	    xmlFree(cur);
	    cur = next;
	}
    }
    if (ctxt->freeAttrs != NULL) {
        xmlAttrPtr cur, next;

	cur = ctxt->freeAttrs;
	while (cur != NULL) {
	    next = cur->next;
	    xmlFree(cur);
	    cur = next;
	}
    }
    /*
     * 清理错误字符串
     */
    if (ctxt->lastError.message != NULL)
        xmlFree(ctxt->lastError.message);
    if (ctxt->lastError.file != NULL)
        xmlFree(ctxt->lastError.file);
    if (ctxt->lastError.str1 != NULL)
        xmlFree(ctxt->lastError.str1);
    if (ctxt->lastError.str2 != NULL)
        xmlFree(ctxt->lastError.str2);
    if (ctxt->lastError.str3 != NULL)
        xmlFree(ctxt->lastError.str3);

#ifdef LIBXML_CATALOG_ENABLED
    if (ctxt->catalogs != NULL)
	xmlCatalogFreeLocal(ctxt->catalogs);
#endif
    xmlFree(ctxt);
}

/**
 * 分配并初始化新的解析器上下文。
 *
 * @returns xmlParserCtxtPtr 或 NULL
 */

xmlParserCtxtPtr
xmlNewParserCtxt(void)
{
    return(xmlNewSAXParserCtxt(NULL, NULL));
}

/**
 * 分配并初始化新的 SAX 解析器上下文。如果 userData 为 NULL，
 * 解析器上下文将作为用户数据传递。
 *
 * @since 2.11.0
 *
 * 如果您想支持较旧的版本，
 * 最好调用 xmlNewParserCtxt() 并通过结构赋值设置 ctxt->sax。
 *
 * @param sax  SAX 处理程序
 * @param userData  用户数据
 * @returns xmlParserCtxtPtr 或内存分配失败时返回 NULL。
 */

xmlParserCtxtPtr
xmlNewSAXParserCtxt(const xmlSAXHandler *sax, void *userData)
{
    xmlParserCtxtPtr ctxt;

    xmlInitParser();

    ctxt = (xmlParserCtxtPtr) xmlMalloc(sizeof(xmlParserCtxt));
    if (ctxt == NULL)
	return(NULL);
    memset(ctxt, 0, sizeof(xmlParserCtxt));
    if (xmlInitSAXParserCtxt(ctxt, sax, userData) < 0) {
        xmlFreeParserCtxt(ctxt);
	return(NULL);
    }
    return(ctxt);
}

/**
 * @since 2.14.0
 *
 * @param ctxt  解析器上下文
 * @returns 私有应用程序数据。
 */
void *
xmlCtxtGetPrivate(xmlParserCtxtPtr ctxt) {
    if (ctxt == NULL)
        return(NULL);

    return(ctxt->_private);
}

/**
 * 设置私有应用程序数据。
 *
 * @since 2.14.0
 *
 * @param ctxt  解析器上下文
 * @param priv  私有应用程序数据
 */
void
xmlCtxtSetPrivate(xmlParserCtxtPtr ctxt, void *priv) {
    if (ctxt == NULL)
        return;

    ctxt->_private = priv;
}

/**
 * @since 2.14.0
 *
 * @param ctxt  解析器上下文
 * @returns 本地目录。
 */
void *
xmlCtxtGetCatalogs(xmlParserCtxtPtr ctxt) {
    if (ctxt == NULL)
        return(NULL);

    return(ctxt->catalogs);
}

/**
 * 设置本地目录。
 *
 * @since 2.14.0
 *
 * @param ctxt  解析器上下文
 * @param catalogs  目录指针
 */
void
xmlCtxtSetCatalogs(xmlParserCtxtPtr ctxt, void *catalogs) {
    if (ctxt == NULL)
        return;

    ctxt->catalogs = catalogs;
}

/**
 * @since 2.14.0
 *
 * @param ctxt  解析器上下文
 * @returns 字典。
 */
xmlDictPtr
xmlCtxtGetDict(xmlParserCtxtPtr ctxt) {
    if (ctxt == NULL)
        return(NULL);

    return(ctxt->dict);
}

/**
 * 设置字典。这应该只在创建解析器上下文后立即完成。
 *
 * @since 2.14.0
 *
 * @param ctxt  解析器上下文
 * @param dict  字典
 */
void
xmlCtxtSetDict(xmlParserCtxtPtr ctxt, xmlDictPtr dict) {
    if (ctxt == NULL)
        return;

    if (ctxt->dict != NULL)
        xmlDictFree(ctxt->dict);

    xmlDictReference(dict);
    ctxt->dict = dict;
}

/**
 * @since 2.14.0
 *
 * @param ctxt  解析器上下文
 * @returns SAX 处理程序结构。这不是副本，不得释放。处理程序可以更新。
 */
xmlSAXHandler *
xmlCtxtGetSaxHandler(xmlParserCtxtPtr ctxt) {
    if (ctxt == NULL)
        return(NULL);

    return(ctxt->sax);
}

/**
 * 将 SAX 处理程序结构设置为 `sax` 的副本。
 *
 * @since 2.14.0
 *
 * @param ctxt  解析器上下文
 * @param sax  SAX 处理程序
 * @returns 成功时返回 0，参数无效或内存分配失败时返回 -1。
 */
int
xmlCtxtSetSaxHandler(xmlParserCtxtPtr ctxt, const xmlSAXHandler *sax) {
    xmlSAXHandler *copy;

    if ((ctxt == NULL) || (sax == NULL))
        return(-1);

    copy = xmlMalloc(sizeof(*copy));
    if (copy == NULL)
        return(-1);

    memcpy(copy, sax, sizeof(*copy));
    ctxt->sax = copy;

    return(0);
}

/**
 * @since 2.14.0
 *
 * @param ctxt  解析器上下文
 * @returns 已解析的文档，如果解析时发生致命错误则返回 NULL。
 * 文档必须由调用者释放。将上下文的文档重置为 NULL。
 */
xmlDocPtr
xmlCtxtGetDocument(xmlParserCtxtPtr ctxt) {
    xmlDocPtr doc;

    if (ctxt == NULL)
        return(NULL);

    if ((ctxt->wellFormed) ||
        (((ctxt->recovery) || (ctxt->html)) &&
         (!xmlCtxtIsCatastrophicError(ctxt)))) {
        doc = ctxt->myDoc;
    } else {
        if (ctxt->errNo == XML_ERR_OK)
            xmlFatalErr(ctxt, XML_ERR_INTERNAL_ERROR, "unknown error");
        doc = NULL;
        xmlFreeDoc(ctxt->myDoc);
    }
    ctxt->myDoc = NULL;

    return(doc);
}

/**
 * @since 2.14.0
 *
 * @param ctxt  解析器上下文
 * @returns 如果这是 HTML 解析器上下文则返回 1，否则返回 0。
 */
int
xmlCtxtIsHtml(xmlParserCtxtPtr ctxt) {
    if (ctxt == NULL)
        return(0);

    return(ctxt->html ? 1 : 0);
}

/**
 * @since 2.14.0
 *
 * @param ctxt  解析器上下文
 * @returns 如果解析器已停止则返回 1，否则返回 0。
 */
int
xmlCtxtIsStopped(xmlParserCtxtPtr ctxt) {
    if (ctxt == NULL)
        return(0);

    return(PARSER_STOPPED(ctxt));
}

#ifdef LIBXML_VALID_ENABLED
/**
 * @since 2.14.0
 *
 * @param ctxt  解析器上下文
 * @returns 验证上下文。
 */
xmlValidCtxtPtr
xmlCtxtGetValidCtxt(xmlParserCtxtPtr ctxt) {
    if (ctxt == NULL)
        return(NULL);

    return(&ctxt->vctxt);
}
#endif

/************************************************************************
 *									*
 *		节点信息处理						*
 *									*
 ************************************************************************/

/**
 * 与 xmlCtxtReset() 相同
 *
 * @deprecated 使用 xmlCtxtReset()
 *
 * @param ctxt  XML 解析器上下文
 */
void
xmlClearParserCtxt(xmlParserCtxtPtr ctxt)
{
    xmlCtxtReset(ctxt);
}


/**
 * 查找给定节点的解析器节点信息结构
 *
 * @deprecated 请勿使用。
 *
 * @param ctx  XML 解析器上下文
 * @param node  树中的 XML 节点
 * @returns xmlParserNodeInfo 块指针或 NULL
 */
const xmlParserNodeInfo *
xmlParserFindNodeInfo(xmlParserCtxtPtr ctx, xmlNodePtr node)
{
    unsigned long pos;

    if ((ctx == NULL) || (node == NULL))
        return (NULL);
    /* 查找节点应该在的位置 */
    pos = xmlParserFindNodeInfoIndex(&ctx->node_seq, node);
    if (pos < ctx->node_seq.length
        && ctx->node_seq.buffer[pos].node == node)
        return &ctx->node_seq.buffer[pos];
    else
        return NULL;
}


/**
 * 初始化（设置为初始状态）节点信息序列
 *
 * @deprecated 请勿使用。
 *
 * @param seq  节点信息序列指针
 */
void
xmlInitNodeInfoSeq(xmlParserNodeInfoSeqPtr seq)
{
    if (seq == NULL)
        return;
    seq->length = 0;
    seq->maximum = 0;
    seq->buffer = NULL;
}

/**
 * 清除（释放内存并重新初始化）节点信息序列
 *
 * @deprecated 请勿使用。
 *
 * @param seq  节点信息序列指针
 */
void
xmlClearNodeInfoSeq(xmlParserNodeInfoSeqPtr seq)
{
    if (seq == NULL)
        return;
    if (seq->buffer != NULL)
        xmlFree(seq->buffer);
    xmlInitNodeInfoSeq(seq);
}

/**
 * 查找给定节点的信息记录在排序序列中的索引位置或应该在的位置。
 *
 * @deprecated 请勿使用。
 *
 * @param seq  节点信息序列指针
 * @param node  XML 节点指针
 * @returns 指示记录位置的长整数
 */
unsigned long
xmlParserFindNodeInfoIndex(xmlParserNodeInfoSeqPtr seq,
                           xmlNodePtr node)
{
    unsigned long upper, lower, middle;
    int found = 0;

    if ((seq == NULL) || (node == NULL))
        return ((unsigned long) -1);

    /* 对键进行二分搜索 */
    lower = 1;
    upper = seq->length;
    middle = 0;
    while (lower <= upper && !found) {
        middle = lower + (upper - lower) / 2;
        if (node == seq->buffer[middle - 1].node)
            found = 1;
        else if (node < seq->buffer[middle - 1].node)
            upper = middle - 1;
        else
            lower = middle + 1;
    }

    /* 返回位置 */
    if (middle == 0 || seq->buffer[middle - 1].node < node)
        return middle;
    else
        return middle - 1;
}


/**
 * 将节点信息记录插入排序序列
 *
 * @deprecated 请勿使用。
 *
 * @param ctxt  XML 解析器上下文
 * @param info  节点信息序列指针
 */
void
xmlParserAddNodeInfo(xmlParserCtxtPtr ctxt,
                     xmlParserNodeInfoPtr info)
{
    unsigned long pos;

    if ((ctxt == NULL) || (info == NULL)) return;

    /* 查找位置并检查节点是否已在序列中 */
    pos = xmlParserFindNodeInfoIndex(&ctxt->node_seq, (xmlNodePtr)
                                     info->node);

    if ((pos < ctxt->node_seq.length) &&
        (ctxt->node_seq.buffer != NULL) &&
        (ctxt->node_seq.buffer[pos].node == info->node)) {
        ctxt->node_seq.buffer[pos] = *info;
    }

    /* 否则，我们需要将新节点添加到缓冲区 */
    else {
        if (ctxt->node_seq.length + 1 > ctxt->node_seq.maximum) {
            xmlParserNodeInfo *tmp;
            int newSize;

            newSize = xmlGrowCapacity(ctxt->node_seq.maximum, sizeof(tmp[0]),
                                      4, XML_MAX_ITEMS);
            if (newSize < 0) {
		xmlCtxtErrMemory(ctxt);
                return;
            }
            tmp = xmlRealloc(ctxt->node_seq.buffer, newSize * sizeof(tmp[0]));
            if (tmp == NULL) {
		xmlCtxtErrMemory(ctxt);
                return;
            }
            ctxt->node_seq.buffer = tmp;
            ctxt->node_seq.maximum = newSize;
        }

        /* 如果位置不在末尾，将元素移开 */
        if (pos != ctxt->node_seq.length) {
            unsigned long i;

            for (i = ctxt->node_seq.length; i > pos; i--)
                ctxt->node_seq.buffer[i] = ctxt->node_seq.buffer[i - 1];
        }

        /* 复制元素并增加长度 */
        ctxt->node_seq.buffer[pos] = *info;
        ctxt->node_seq.length++;
    }
}

/************************************************************************
 *									*
 *		默认设置						*
 *									*
 ************************************************************************/
/**
 * 设置并返回启用严格警告的先前值。
 *
 * @deprecated 使用带有 XML_PARSE_PEDANTIC 的现代选项 API。
 *
 * @param val  int 0 或 1
 * @returns 最后的值，0 表示不替换，1 表示替换。
 */

int
xmlPedanticParserDefault(int val) {
    int old = xmlPedanticParserDefaultValue;

    xmlPedanticParserDefaultValue = val;
    return(old);
}

/**
 * 设置并返回在元素内容中启用行号的先前值。
 * 这可能会破坏旧应用程序，默认情况下是关闭的。
 *
 * @deprecated 现代选项 API 总是启用行号。
 *
 * @param val  int 0 或 1
 * @returns 最后的值，0 表示不替换，1 表示替换。
 */

int
xmlLineNumbersDefault(int val) {
    int old = xmlLineNumbersDefaultValue;

    xmlLineNumbersDefaultValue = val;
    return(old);
}

/**
 * 设置并返回默认实体支持的先前值。
 * 最初解析器总是保留实体引用而不是在输出中替换实体值。
 * 必须使用此函数来更改默认解析器行为。
 * SAX::substituteEntities() 必须用于逐文件更改。
 *
 * @deprecated 使用带有 XML_PARSE_NOENT 的现代选项 API。
 *
 * @param val  int 0 或 1
 * @returns 最后的值，0 表示不替换，1 表示替换。
 */

int
xmlSubstituteEntitiesDefault(int val) {
    int old = xmlSubstituteEntitiesDefaultValue;

    xmlSubstituteEntitiesDefaultValue = val;
    return(old);
}

/**
 * 设置并返回默认空白文本节点支持的先前值。
 * 解析器的 1.x 版本使用启发式方法尝试检测可忽略的空白。
 * 结果是 SAX 回调生成 xmlSAX2IgnorableWhitespace() 回调而不是 characters() 回调，
 * 当使用 DOM 输出时，包含这些空白的文本节点不会生成。
 * 2.x 及更高版本将切换到 XML 标准方式，
 * ignorableWhitespace() 只有在验证模式下运行解析器时
 * 以及当前元素不允许 CDATA 或混合内容时才会生成。
 * 提供此函数是为了在 1.X 库上强制标准行为，
 * 并在 2.X 上运行 1.X 客户端代码时切换回旧模式以保持兼容性。
 * 1.X 代码的升级应该通过使用 xmlIsBlankNode() 便利函数
 * 来检测生成的"空"节点来完成。
 * 此值还会影响保存代码时缩进的自动生成，
 * 如果保留空白部分，则不会生成缩进。
 *
 * @deprecated 使用带有 XML_PARSE_NOBLANKS 的现代选项 API。
 *
 * @param val  int 0 或 1
 * @returns 最后的值，0 表示不替换，1 表示替换。
 */

int
xmlKeepBlanksDefault(int val) {
    int old = xmlKeepBlanksDefaultValue;

    xmlKeepBlanksDefaultValue = val;
#ifdef LIBXML_OUTPUT_ENABLED
    if (!val)
        xmlIndentTreeOutput = 1;
#endif
    return(old);
}

